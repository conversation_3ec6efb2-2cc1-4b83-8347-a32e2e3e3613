namespace MRI.OTA.Application.Models.FeatureFlags
{
    /// <summary>
    /// Information about a feature flag
    /// </summary>
    public class FeatureFlagInfo
    {
        /// <summary>
        /// Name of the feature flag
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Whether the feature flag is currently enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Description of what the feature flag controls
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// When the feature flag status was last checked
        /// </summary>
        public DateTime CheckedAt { get; set; }

        /// <summary>
        /// Source of the feature flag (Configuration, Azure App Configuration, etc.)
        /// </summary>
        public string Source { get; set; } = "Configuration";

        /// <summary>
        /// Additional metadata about the feature flag
        /// </summary>
        public Dictionary<string, object>? Metadata { get; set; }
    }



    /// <summary>
    /// Model for feature flag evaluation context
    /// </summary>
    public class FeatureFlagContext
    {
        /// <summary>
        /// User identifier
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// User roles
        /// </summary>
        public List<string>? UserRoles { get; set; }

        /// <summary>
        /// Additional context properties for feature evaluation
        /// </summary>
        public Dictionary<string, object>? Properties { get; set; }
    }
}
