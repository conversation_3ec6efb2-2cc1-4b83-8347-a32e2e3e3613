{"ConnectionStrings": {"DefaultConnection": "#{ota-db-connection-string}"}, "ApplicationOption": {"BaseUrl": "#{ota-base-url}", "DefaultImageUrl": "#{ota-default-image-url}", "BatchSize": "#{ota-batch-size}"}, "Authentication": {"TenantId": "#{ota-tenant-id}", "TenantName": "#{ota-tenant-name}", "ClientId": "#{ota-client-id}", "ClientSecret": "#{ota-client-secret}", "B2CInstance": "#{ota-b2c-instance}", "Domain": "#{ota-b2c-domain}", "SignUpSignInPolicyId": "#{ota-signup-signin-policyid}", "Scopes": ["openid", "access_api"]}, "Cors": {"AllowedOrigins": "#{ota-api-allowed-origins}"}, "AzureStorage": {"ConnectionString": "#{ota-storage-connection-string}", "ContainerName": "#{ota-storage-container-name}"}, "ImageUploadSettings": {"AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"], "MaxFileSizeInBytes": 20}, "SendGridSettings": {"ApiKey": "#{ota-sendgrid-api-key}", "DefaultFromEmail": "#{ota-sendgrid-from-email}", "DefaultFromName": "#{ota-sendgrid-from-name}"}, "AzureCommunicationSettings": {"ConnectionString": "#{ota-azure-communication-connection-string}", "DefaultFromEmail": "#{ota-azure-communication-from-email}"}, "FeatureManagement": {"ConnectionString": "#{ota-feature-configuration-connection-string}"}, "RedisConfig": {"ConnectionString": "#{ota-redis-connection-string}", "DefaultExpirationMinutes": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.ApplicationInsights"], "WriteTo": [{"Name": "ApplicationInsights", "Args": {"connectionString": "#{ota-app-insight-connection-string}", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithThreadName", "WithEventType"], "Properties": {"Application": "MRI OTA APIs"}}, "ApplicationInsights": {"ConnectionString": "#{ota-app-insight-connection-string}"}, "FirebaseSettings": {"type": "#{ota-firebase-type}", "project_id": "#{ota-firebase-project-id}", "private_key_id": "#{ota-firebase-private-key-id}", "private_key": "#{ota-firebase-private-key}", "client_email": "#{ota-firebase-client-email}", "client_id": "#{ota-firebase-client-id}", "auth_uri": "#{ota-firebase-auth-uri}", "token_uri": "#{ota-firebase-token-uri}", "auth_provider_x509_cert_url": "#{ota-firebase-auth-provider-x509-cert-url}", "client_x509_cert_url": "#{ota-firebase-client-x509-cert-url}", "universe_domain": "#{ota-firebase-universe-domain}"}}