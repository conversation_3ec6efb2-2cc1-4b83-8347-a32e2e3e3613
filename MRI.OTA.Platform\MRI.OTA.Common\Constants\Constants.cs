﻿namespace MRI.OTA.Common.Constants
{
    /// <summary>
    /// Constants
    /// </summary>
    public static class Constants
    {
        #region Enum Data Source Type
        public enum DataSourceTypeEnum
        {
            SelfSource = 1,
            Verdaccio = 2,
            PropertyTree = 3
        }
        #endregion

        #region Enum Property RelationShip
        public static readonly Dictionary<string, int> PropertyRelationShipDic = new Dictionary<string, int>
        {
            { "Owner", 1 },
            { "Owner Managed", 2 },
            { "Tenant", 3 },
            { "Co-Tenant", 4 },
            { "Owner Occupier", 5 },
            { "Prospective Owner", 6 },
            { "Prospective Tenant", 7 },
        };
        
        public enum PropertyRelationShipType
        {
            Owner = 1,
            OwnerManaged = 2,
            Tenant = 3,
            CoTenant = 4,
            OwnerOccupier = 5,
            ProspectiveOwner = 6,
            ProspectiveTenant = 7
        }
        public enum DataTypeName
        {
            inspections,
            maintenance,
            documents
        }
        #endregion

        public enum APIDetail
        {
            GetAgencyDetails = 1,
            GetAssociatePortfolio = 2,
            GetProperties = 3,
            GetManagement = 4,
            GetTenanciesTenant = 5,
            GetTenanciesOwner = 6,
            GetFinancials = 7,
            GetMaintenance = 8,
            GetInspections = 9,
            GetCompliance = 10,
            GetDocuments = 11,
            GetAgencyPartnersDetails = 12
        }
        public static readonly Dictionary<string, int> StatusDic = new Dictionary<string, int>
        {
            { "Pending Quote", 1 },
            { "In Progress", 2 },
            { "Confirmed", 3 },
            { "Completed", 4 },
            { "Cancelled", 5 },
            { "On Hold", 6 },
            { "Scheduled", 7 },
            { "Awaiting Parts", 8 },
            { "Awaiting Approval", 9 }
        };
        public static readonly Dictionary<string, int> RequestStatusDic = new Dictionary<string, int>
        {
            { "Open", 1 },
            { "Closed", 2 },
            { "Approved", 3 },
            { "Rejected", 4 },
            { "Under Review", 5 },
            { "Escalated", 6 },
            { "Resolved", 7 },
            { "Cancelled", 8 }
        };

        #region Constants for table names
        /// <summary>
        /// User table name
        /// </summary>
        public const string UserTableName = "Users";
        /// <summary>
        /// Address table name
        /// </summary>
        public const string AddressTableName = "Addresses";
        /// <summary>
        /// User property table name
        /// </summary>
        public const string UserPropertyTableName = "UserProperties";

        /// <summary>
        /// PropertyDetails table name
        /// </summary>
        public const string PropertyDetailsTableName = "PropertyDetails";

        /// <summary>
        /// PropertyManagerInformation table name
        /// </summary>
        public const string PropertyManagerInformationTableName = "PropertyManagerInformation";

        /// <summary>
        /// PropertyFinancialInformation table name
        /// </summary>
        public const string PropertyFinancialInformationTableName = "PropertyFinancialInformation";

        /// <summary>
        /// UserDataSource table name
        /// </summary>
        public const string UserDataSourceTableName = "UserDataSource";

        /// <summary>
        /// UserPropertiesNickName table name
        /// </summary>
        public const string UserPropertiesNickNameTableName = "UserPropertiesNickName";

        /// <summary>
        /// Modules table name
        /// </summary>
        public const string ModulesTableName = "Modules";

        /// <summary>
        /// ModulePropertyRelationship table name
        /// </summary>
        public const string ModulePropertyRelationshipTableName = "ModulePropertyRelationship";

        /// <summary>
        /// DataSourceTableName table name
        /// </summary>
        public const string DataSourceTableName = "DataSource";

        /// <summary>
        /// PostalAddressesTableName table name
        /// </summary>
        public const string PostalAddressesTableName = "PostalAddresses";
        /// Countries table name
        /// </summary>
        public const string CountriesTableName = "Countries";
        /// <summary>
        /// PropertyImages table name
        /// </summary>
        public const string PropertyImagesTableName = "PropertyImages";

        /// <summary>
        /// State table name
        /// </summary>
        public const string StateTableName = "State";

        /// <summary>
        /// OccupancyTypes table name
        /// </summary>
        public const string OccupancyTypesTableName = "OccupancyTypes";

        /// <summary>
        /// PropertyRelationship table name
        /// </summary>
        public const string PropertyRelationshipTableName = "PropertyRelationship";

        /// <summary>
        /// OccupancyStatusType table name
        /// </summary>
        public const string OccupancyStatusTypeTableName = "OccupancyStatusType";

        /// <summary>
        /// AdministrativeAreas table name
        /// </summary>
        public const string AdministrativeAreasTableName = "AdministrativeAreas";

        /// <summary>
        /// User Invites table name
        /// </summary>
        public const string UserInvitesTableName = "UserInvites";
        public const string TenanciesOwnerDetailsTableName = "TenanciesOwnerDetails";
        public const string MaintenanceDetailsTableName = "MaintenanceDetails";
        public const string InspectionDetailsTableName = "InspectionDetails";
        public const string ComplianceDetailsTableName = "ComplianceDetails";
        public const string DocumentDetailsTableName = "DocumentDetails";
        public const string APITrackingDetailTableName = "APITrackingDetail";
        
        /// <summary>
        /// AgencyDetails table name
        /// </summary>
        public const string AgencyDetailsTableName = "AgencyDetails";
        public const string AgencyPartnersTableName = "AgencyPartners";
        public const string CurrencyDetailTableName = "CurrencyDetail";
        public const string UserDeviceDetailsTableName = "UserDeviceDetails";
        public const string NotificationMasterTableName = "NotificationMaster";
        public const string UserNotificationDetailsTableName = "UserNotificationDetails";
        public const string ArchivePropertyAgencyDetailsTableName = "ArchivePropertyAgencyDetails";
        #endregion

        /// <summary>
        /// Success
        /// </summary>
        public const int Success = 1;

        /// <summary>
        /// Error
        /// </summary>
        public const int Error = -1;

        /// <summary>
        /// Error
        /// </summary>
        public const int DefaultImageExists = -2;

        /// <summary>
        /// Error
        /// </summary>
        public const int InvitationLinkExpires = -2;

        /// <summary>
        /// Duplicate Error - indicates that an item already exists
        /// </summary>
        public const int DuplicateError = -3;

        /// <summary>
        /// NotAddDefaultImage
        /// </summary>
        public const int NotAddDefaultImageIndex = -1;

        /// <summary>
        /// PropertyOwnershipDetails table name
        /// </summary>
        public const string SqlDateTimeFormat = "yyyy-MM-dd HH:mm:ss.fff";

        /// <summary>
        /// PropertyOwnershipDetails table name
        /// </summary>
        public const string SqlDateTimeOffsetFormat = "yyyy-MM-dd HH:mm:ss.fff zzz";

        /// <summary>
        /// Self Source Data Source Name
        /// </summary>
        public const string SelfSourceDataSourceName = "MRI OTA Self-Source";

        /// <summary>
        /// Self Source Data Source Name
        /// </summary>
        public const string VerdaccioDataSourceName = "Verdaccio";

        public const string PropertyTreeDataSourceName = "Property tree";
        public const string IntegrationMockDataSourceName = "Integration Mock";

        public static readonly string[] DataSourceNames = new[] { Constants.PropertyTreeDataSourceName, Constants.IntegrationMockDataSourceName };

        /// <summary>
        /// Dictionary constant containing integration endpoint key-value pairs
        /// </summary>
        public static readonly Dictionary<string, string> integrationEndpoints = new Dictionary<string, string>
        {
            { "AssociatePortfolio", "associate-portfolio" },
            { "Properties-Owner", "properties-owner" },
            { "Properties-Tenant", "properties-tenant" },
            { "Management", "management" },
            { "TenanciesTenant", "tenancies-tenant" },
            { "TenanciesOwner", "tenancies-owner" },
            { "Financials", "financials" },
            { "Maintenance", "maintenance" },
            { "Inspections", "inspections" },
            { "Compliance", "compliance" },
            { "Documents", "documents" },
            { "AgencyDetails", "agency-details" },
            { "AgencyPartners", "agency-partners" },
            { "DisconnectUser", "disconnect-user" }
        };

        /// <summary>
        /// Array containing client credential endpoints that require client credentials flow validation
        /// </summary>
        public static readonly string[] ClientCredentialEndpoints = new[] 
        {
            "/api/v1/token",
            "/api/v1/invitations/invite-user", 
            "/api/v1/integrations", 
            "/api/v1/openid-configuration"
        };

        /// <summary>
        /// Tenancy id column name
        /// </summary>
        public const string TenancyIdColumnName = "SRCTenancyId";

        /// <summary>
        /// ManagementId ColumnName
        /// </summary>
        public const string ManagementIdColumnName = "SRCManagementId";

        /// <summary>
        /// Provider type
        /// </summary>
        public static readonly Dictionary<string, int> ProviderType = new Dictionary<string, int>
        {
            { "LocalAccount", 1 },
            { "google.com", 2 },
            { "facebook.com", 3 },
            { "https://appleid.apple.com", 4 },
        };

        /// <summary>
        /// Provider type name
        /// </summary>
        public static readonly Dictionary<int, string> ProviderTypeName = new Dictionary<int, string>
        {
            { 1 ,"Azure B2C"},
            { 2 ,"Google"},
            { 3 ,"Facebook"},
            { 4 ,"Apple"},
        };

        public enum IntegrationEndPointsType
        {
            TenantPropertyList,
            OwnerPropertyList,
            TenanciesTenantList,
            ManagementList,
            TenanciesOwnerList,
            MaintenanceList,
            InspectionList,
            ComplianceList,
            FinancialList,
            DocumentList,
            AgencyList,
            AgencyPartnerList,
        }
        #region Notification Categories
        public const string NewPropertyAdded = "NEW_PROPERTY_ADDED";
        public const string PropertyUpdated = "PROPERTY_UPDATED";
        public const string NewMaintenanceAdded = "NEW_MAINTENANCE_ADDED";
        public const string NewInspectionAdded = "NEW_INSPECTION_ADDED";
        public const string DOCUMENTSHARED = "DOCUMENT_SHARED";
        #endregion

        public enum APIStatus
        {
            Success = 1,
            Error = -1,
            DuplicateError = -2,
            NotAddDefaultImageIndex = -3,
            DefaultImageExists = -4,
            InvitationLinkExpires = -5,
            AlredyExists = -6,

        }
    }
}
