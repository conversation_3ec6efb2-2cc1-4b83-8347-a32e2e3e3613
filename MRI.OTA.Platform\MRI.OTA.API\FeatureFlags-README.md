# Feature Flags with Azure App Configuration

This document explains how to set up and use Feature Flags with Azure App Configuration in the MRI OTA API.

## Overview

The Feature Flags implementation supports two modes:
1. **Azure App Configuration** - Feature flags stored in Azure App Configuration service
2. **Local Configuration** - Feature flags stored in local `appsettings.json` files (fallback)

## Azure App Configuration Setup

### 1. Create Azure App Configuration Resource

1. Go to Azure Portal
2. Create a new "App Configuration" resource
3. Choose your subscription, resource group, and location
4. Note down the connection string from the "Access keys" section

### 2. Configure Connection String

Add the Azure App Configuration connection string to your configuration files:

**appsettings.json** (Production):
```json
{
  "FeatureManagement": {
    "ConnectionString": "#{ota-azure-app-configuration-connection-string}"
  }
}
```

**appsettings.Development.json** (Development):
```json
{
  "FeatureManagement": {
    "ConnectionString": "Endpoint=https://appconfig-nw02-shrdplt-dev.azconfig.io;Id=****;Secret=8YcTjZlIWDbGtjLaiFSqK6bQLtguRFwukHsZm9fcG4tn6cTtQSEeJQQJ99BEACL93NafTAzNAAACAZACnJ97"
  }
}
```

### 3. Create Feature Flags in Azure App Configuration

1. Go to your Azure App Configuration resource
2. Navigate to "Feature manager"
3. Create feature flags with the following naming convention:
   - `EnableNewFeature`
   - `EnableBetaFeatures` 
   - `EnableAdvancedLogging`

### 4. Configure Feature Flag Targeting (Optional)

You can configure feature flags to target specific users or groups:

1. In Azure App Configuration, edit a feature flag
2. Enable "Use feature filter"
3. Add filters like:
   - **Percentage Filter**: Enable for a percentage of users
   - **Time Window Filter**: Enable during specific time periods
   - **Custom Filters**: Target specific users or groups

## API Endpoints

### Get All Feature Flags
```
GET /api/v1/feature-flags
```

Returns all feature flags for the authenticated user based on their email address.

**Response:**
```json
{
  "success": true,
  "statusCode": 200,
  "message": "Feature flags retrieved successfully",
  "data": [
    {
      "featureName": "EnableNewFeature",
      "isEnabled": true,
      "userEmail": "<EMAIL>",
      "description": "Enables the new feature functionality",
      "checkedAt": "2024-01-15T10:30:00Z",
      "source": "Azure App Configuration",
      "metadata": null
    }
  ]
}
```

## Predefined Feature Flags

The system works with the following predefined feature flags:

- `EnableNewFeature` - Enables the new feature functionality
- `EnableBetaFeatures` - Enables beta features for testing
- `EnableAdvancedLogging` - Enables advanced logging capabilities

These feature flags should be created in Azure App Configuration with these exact names.

## Current Setup

The development environment is already configured with the Azure App Configuration connection string:

```json
{
  "FeatureManagement": {
    "ConnectionString": "Endpoint=https://appconfig-nw02-shrdplt-dev.azconfig.io;Id=****;Secret=8YcTjZlIWDbGtjLaiFSqK6bQLtguRFwukHsZm9fcG4tn6cTtQSEeJQQJ99BEACL93NafTAzNAAACAZACnJ97"
  }
}
```

You just need to create the feature flags in the Azure App Configuration portal and the API will automatically retrieve them.

## Email-Based Feature Targeting

Feature flags are evaluated based on the authenticated user's email address from `TaskContext.Email`. This allows for:

- User-specific feature rollouts
- Email domain-based targeting
- Gradual feature rollouts

## Caching

Feature flags are cached for 5 minutes to reduce calls to Azure App Configuration and improve performance.

## Environment Variables

For production deployment, set the following environment variable or use Azure DevOps variable replacement:

```
ota-azure-app-configuration-connection-string=Endpoint=https://your-appconfig.azconfig.io;Id=your-id;Secret=your-secret
```

## Monitoring

The application logs feature flag evaluations and source information:

- Feature flag checks are logged with user email
- Source of feature flags (Azure vs Local) is indicated in responses
- Connection issues with Azure App Configuration are logged as errors

## Security

- Connection strings should be stored securely (Azure Key Vault recommended)
- Use managed identity when possible instead of connection strings
- Feature flags can contain sensitive configuration, ensure proper access controls

## Troubleshooting

1. **Feature flags not updating**: Check cache expiration (5 minutes)
2. **Connection issues**: Verify connection string and network connectivity
3. **Fallback to local config**: Check logs for Azure App Configuration connection errors
4. **User-specific targeting not working**: Verify email is available in TaskContext

## Next Steps

1. Set up Azure App Configuration resource
2. Configure connection string in your environment
3. Create feature flags in Azure portal
4. Test the API endpoints
5. Configure user targeting as needed
