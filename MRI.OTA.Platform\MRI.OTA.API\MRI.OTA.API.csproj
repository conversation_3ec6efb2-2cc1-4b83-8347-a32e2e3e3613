﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>0cb6ba5f-0a8a-4855-a16a-02a09c322d08</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MRI.OTA.Application\MRI.OTA.Application.csproj" />
    <ProjectReference Include="..\MRI.OTA.Infrastructure\MRI.OTA.Infrastructure.csproj" />
  </ItemGroup>
	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="Asp.Versioning.Http" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
		<PackageReference Include="FluentValidation" />
		<PackageReference Include="FluentValidation.AspNetCore" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
		<PackageReference Include="Microsoft.Extensions.Configuration" />
		<PackageReference Include="Microsoft.FeatureManagement.AspNetCore" />
		<PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
		<PackageReference Include="Serilog.Enrichers.Environment" />
		<PackageReference Include="Serilog.Enrichers.Thread" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Settings.Configuration" />
		<PackageReference Include="Serilog.Enrichers.Process" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" />
		<PackageReference Include="Serilog.Sinks.Console" />
		<PackageReference Include="Serilog.Sinks.File" />
		<PackageReference Include="Swashbuckle.AspNetCore" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
		<!-- Logging -->
		<PackageReference Include="Microsoft.Extensions.Logging" />
		<!-- HTTP Clients -->
		<PackageReference Include="Microsoft.Extensions.Http" />
		<PackageReference Include="UAParser" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Templates\" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="Templates\Invitation_MyPlace.html">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Templates\InvitationAccepted_MyPlace.html">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Templates\Migration_MyPlace.html">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>
