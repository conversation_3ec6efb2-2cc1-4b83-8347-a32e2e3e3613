namespace MRI.OTA.Application.Models.FeatureFlags
{
    /// <summary>
    /// Response model for feature flag status
    /// </summary>
    public class FeatureFlagResponseModel
    {
        /// <summary>
        /// Name of the feature flag
        /// </summary>
        public string FeatureName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the feature flag is enabled for the user
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// User email for whom the feature flag was checked
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// Description of what the feature flag controls
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Timestamp when the flag was checked
        /// </summary>
        public DateTime CheckedAt { get; set; }

        /// <summary>
        /// Source of the feature flag (Configuration, Azure App Configuration, etc.)
        /// </summary>
        public string Source { get; set; } = "Configuration";

        /// <summary>
        /// Additional metadata about the feature flag
        /// </summary>
        public Dictionary<string, object>? Metadata { get; set; }
    }
}
