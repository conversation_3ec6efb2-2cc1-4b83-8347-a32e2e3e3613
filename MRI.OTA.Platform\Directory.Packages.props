<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Core ASP.NET and Dependency Injection -->
    <PackageVersion Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageVersion Include="Azure.Storage.Blobs" Version="12.24.0" />
    <PackageVersion Include="FirebaseAdmin" Version="3.1.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageVersion Include="Microsoft.FeatureManagement.AspNetCore" Version="3.5.0" />
    <PackageVersion Include="Azure.Data.AppConfiguration" Version="1.4.1" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.0.0" />
    <PackageVersion Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageVersion Include="AutoMapper" Version="14.0.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.3" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageVersion Include="MRI.Integration.Consumer.SDK" Version="1.0.24326.1" />
    <PackageVersion Include="MRI.Integration.SDK.Shared" Version="1.0.24326.1" />
    <PackageVersion Include="SendGrid" Version="9.29.3" />
    <PackageVersion Include="Serilog" Version="4.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageVersion Include="Dapper" Version="2.1.66" />
    <PackageVersion Include="FluentValidation" Version="11.10.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    <PackageVersion Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
    <!-- Logging -->
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.3" />
    <!-- HTTP Clients -->
    <PackageVersion Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <!-- Testing Dependencies -->
    <PackageVersion Include="Moq" Version="4.18.2" />
    <PackageVersion Include="UAParser" Version="3.1.47" />
    <PackageVersion Include="xunit" Version="2.5.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.6" />
	  <!-- Azure Function -->
	<PackageVersion Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
	<PackageVersion Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.0" />
	<PackageVersion Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" />
	<PackageVersion Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.1" />
	<PackageVersion Include="Microsoft.ApplicationInsights" Version="2.22.0" />
	<PackageVersion Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
	<PackageVersion Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.4.0" />
  </ItemGroup>
</Project>