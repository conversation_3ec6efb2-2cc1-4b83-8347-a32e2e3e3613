using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.Controllers.FeatureFlags.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.FeatureFlags;
using MRI.OTA.Common.Models;

namespace MRI.OTA.Tests.FeatureFlags.Controller
{
    public class UnitTestFeatureFlagsController
    {
        private readonly Mock<IFeatureFlagService> _mockFeatureFlagService;
        private readonly Mock<ILogger<FeatureFlagsController>> _mockLogger;
        private readonly TaskContext _taskContext;
        private readonly FeatureFlagsController _controller;

        public UnitTestFeatureFlagsController()
        {
            _mockFeatureFlagService = new Mock<IFeatureFlagService>();
            _mockLogger = new Mock<ILogger<FeatureFlagsController>>();
            _taskContext = new TaskContext { UserId = 123 };
            _controller = new FeatureFlagsController(_mockFeatureFlagService.Object, _mockLogger.Object, _taskContext);
        }

        [Fact]
        public async Task GetFeatureFlag_ValidFeatureName_ReturnsOkResult()
        {
            // Arrange
            var featureName = "EnableNewFeature";
            var expectedIsEnabled = true;
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync(featureName, "123"))
                .ReturnsAsync(expectedIsEnabled);

            // Act
            var result = await _controller.GetFeatureFlag(featureName);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<FeatureFlagResponse>>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal(featureName, response.Data.FeatureName);
            Assert.Equal(expectedIsEnabled, response.Data.IsEnabled);
            Assert.Equal(123, response.Data.UserId);
        }

        [Fact]
        public async Task GetFeatureFlag_EmptyFeatureName_ReturnsBadRequest()
        {
            // Arrange
            var featureName = "";

            // Act
            var result = await _controller.GetFeatureFlag(featureName);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(response.Success);
            Assert.Contains("Feature name cannot be empty", response.Message);
        }

        [Fact]
        public async Task GetAllFeatureFlags_ReturnsOkResult()
        {
            // Arrange
            var featureFlagInfos = new List<FeatureFlagInfo>
            {
                new FeatureFlagInfo { Name = "EnableNewFeature", IsEnabled = true, Description = "Test feature" },
                new FeatureFlagInfo { Name = "EnableBetaFeatures", IsEnabled = false, Description = "Beta features" }
            };

            _mockFeatureFlagService.Setup(s => s.GetAllFeatureFlagsAsync())
                .ReturnsAsync(featureFlagInfos);
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableNewFeature", "123"))
                .ReturnsAsync(true);
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableBetaFeatures", "123"))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.GetAllFeatureFlags();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<List<FeatureFlagResponse>>>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal(2, response.Data.Count);
            Assert.Equal("EnableNewFeature", response.Data[0].FeatureName);
            Assert.True(response.Data[0].IsEnabled);
            Assert.Equal(123, response.Data[0].UserId);
        }

        [Fact]
        public async Task CheckFeatureFlags_ValidRequest_ReturnsOkResult()
        {
            // Arrange
            var request = new FeatureFlagCheckRequest
            {
                FeatureNames = new List<string> { "EnableNewFeature", "EnableBetaFeatures" }
            };

            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableNewFeature", "123"))
                .ReturnsAsync(true);
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableBetaFeatures", "123"))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.CheckFeatureFlags(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<List<FeatureFlagResponse>>>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal(2, response.Data.Count);
            Assert.Equal("EnableNewFeature", response.Data[0].FeatureName);
            Assert.True(response.Data[0].IsEnabled);
            Assert.Equal("EnableBetaFeatures", response.Data[1].FeatureName);
            Assert.False(response.Data[1].IsEnabled);
        }

        [Fact]
        public async Task CheckFeatureFlags_EmptyRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new FeatureFlagCheckRequest
            {
                FeatureNames = new List<string>()
            };

            // Act
            var result = await _controller.CheckFeatureFlags(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(response.Success);
            Assert.Contains("Feature names list cannot be empty", response.Message);
        }

        [Fact]
        public async Task CheckFeatureFlags_NullRequest_ReturnsInternalServerError()
        {
            // Arrange
            FeatureFlagCheckRequest? request = null;

            // Act
            var result = await _controller.CheckFeatureFlags(request!);

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result.Result);
            Assert.Equal(500, objectResult.StatusCode);
            var response = Assert.IsType<ApiResponse<object>>(objectResult.Value);
            Assert.False(response.Success);
            Assert.Contains("An error occurred while checking feature flags", response.Message);
        }

        [Fact]
        public async Task GetFeatureFlag_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var featureName = "EnableNewFeature";
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync(featureName, "123"))
                .ThrowsAsync(new Exception("Service error"));

            // Act
            var result = await _controller.GetFeatureFlag(featureName);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result.Result);
            Assert.Equal(500, statusCodeResult.StatusCode);
            var response = Assert.IsType<ApiResponse<object>>(statusCodeResult.Value);
            Assert.False(response.Success);
            Assert.Contains("An error occurred while retrieving the feature flag", response.Message);
        }

        [Fact]
        public void Constructor_ValidParameters_CreatesInstance()
        {
            // Arrange & Act
            var controller = new FeatureFlagsController(
                _mockFeatureFlagService.Object,
                _mockLogger.Object,
                _taskContext);

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public async Task GetFeatureFlag_WhitespaceFeatureName_ReturnsBadRequest()
        {
            // Arrange
            var featureName = "   ";

            // Act
            var result = await _controller.GetFeatureFlag(featureName);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(response.Success);
            Assert.Contains("Feature name cannot be empty", response.Message);
        }

        [Fact]
        public async Task CheckFeatureFlags_FiltersOutEmptyFeatureNames_ReturnsOkResult()
        {
            // Arrange
            var request = new FeatureFlagCheckRequest
            {
                FeatureNames = new List<string> { "EnableNewFeature", "", "   ", "EnableBetaFeatures" }
            };

            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableNewFeature", "123"))
                .ReturnsAsync(true);
            _mockFeatureFlagService.Setup(s => s.IsFeatureEnabledForUserAsync("EnableBetaFeatures", "123"))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.CheckFeatureFlags(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<ApiResponse<List<FeatureFlagResponse>>>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal(2, response.Data.Count); // Only valid feature names should be processed
            Assert.Equal("EnableNewFeature", response.Data[0].FeatureName);
            Assert.Equal("EnableBetaFeatures", response.Data[1].FeatureName);
        }
    }
}
