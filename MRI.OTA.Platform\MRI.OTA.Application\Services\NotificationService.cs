﻿using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace MRI.OTA.Application.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly INotificationRepository _notificationRepository;

        public NotificationService(ILogger<NotificationService> logger, IConfiguration configuration, INotificationRepository notificationRepository )
        {
            _logger = logger;
            _configuration = configuration;
            _notificationRepository = notificationRepository;
            try
            {
                // Initialize the Firebase app if it hasn't been initialized yet
                //if (FirebaseApp.DefaultInstance == null)
                //{
                //    // Get Firebase settings from configuration
                //    var firebaseSettings = _configuration.GetSection("FirebaseSettings").Get<FirebaseSettings>();
                //    if (firebaseSettings == null)
                //    {
                //        _logger.LogError("FirebaseSettings section is missing or empty in appsettings.json.");
                //        return;
                //    }

                //    // Log Firebase settings (excluding the full private key for security)
                //    _logger.LogInformation("Initializing Firebase with project: {ProjectId}, Client email: {ClientEmail}",
                //        firebaseSettings.Project_Id,
                //        firebaseSettings.Client_Email);

                //    // Create a new JSON object with all the Firebase settings
                //    var firebaseJson = new JObject();
                //    firebaseJson["type"] = firebaseSettings.Type;
                //    firebaseJson["project_id"] = firebaseSettings.Project_Id;
                //    firebaseJson["private_key_id"] = firebaseSettings.Private_Key_Id;
                    
                //    // Handle the private key specifically to ensure proper formatting
                //    string privateKey = firebaseSettings.Private_Key;
                //    if (string.IsNullOrEmpty(privateKey))
                //    {
                //        _logger.LogError("Firebase private_key is null or empty");
                //        return;
                //    }
                    
                //    // Check if the private key is base64 encoded
                //    if (IsBase64String(privateKey))
                //    {
                //        _logger.LogInformation("Private key appears to be base64 encoded, decoding...");
                //        try
                //        {
                //            byte[] data = Convert.FromBase64String(privateKey);
                //            privateKey = Encoding.UTF8.GetString(data);
                //            _logger.LogInformation("Successfully decoded base64 private key");
                //        }
                //        catch (Exception ex)
                //        {
                //            _logger.LogError(ex, "Failed to decode base64 private key");
                //            // Continue with the original key
                //        }
                //    }
                    
                //    // Fix the private key formatting
                //    // 1. Replace escaped newlines (\\n) with actual newlines
                //    privateKey = Regex.Replace(privateKey, @"\\n", "\n");
                    
                //    // 2. Make sure it has the proper BEGIN/END markers with newlines
                //    if (!privateKey.Contains("-----BEGIN PRIVATE KEY-----"))
                //    {
                //        privateKey = "-----BEGIN PRIVATE KEY-----\n" + privateKey;
                //    }
                    
                //    if (!privateKey.Contains("-----END PRIVATE KEY-----"))
                //    {
                //        privateKey = privateKey + "\n-----END PRIVATE KEY-----";
                //    }
                    
                //    // 3. Ensure there's a newline after BEGIN and before END if not already there
                //    privateKey = privateKey.Replace("-----BEGIN PRIVATE KEY-----", "-----BEGIN PRIVATE KEY-----\n");
                //    privateKey = privateKey.Replace("-----END PRIVATE KEY-----", "\n-----END PRIVATE KEY-----");
                    
                //    // Log private key info for debugging
                //    _logger.LogDebug("Private key length: {Length}", privateKey.Length);
                    
                //    // Add the properly formatted private key to the JSON object
                //    firebaseJson["private_key"] = privateKey;
                    
                //    // Add the rest of the Firebase settings
                //    firebaseJson["client_email"] = firebaseSettings.Client_Email;
                //    firebaseJson["client_id"] = firebaseSettings.Client_Id;
                //    firebaseJson["auth_uri"] = firebaseSettings.Auth_Uri;
                //    firebaseJson["token_uri"] = firebaseSettings.Token_Uri;
                //    firebaseJson["auth_provider_x509_cert_url"] = firebaseSettings.Auth_Provider_X509_Cert_Url;
                //    firebaseJson["client_x509_cert_url"] = firebaseSettings.Client_X509_Cert_Url;
                //    firebaseJson["universe_domain"] = firebaseSettings.Universe_Domain;
                    
                //    // Convert the JObject to a JSON string
                //    string firebaseJsonString = firebaseJson.ToString(Formatting.None);
                    
                //    // Create Firebase credentials from the JSON string
                //    var credentials = GoogleCredential.FromJson(firebaseJsonString);
                    
                //    // Create the Firebase app
                //    FirebaseApp.Create(new AppOptions
                //    {
                //        Credential = credentials
                //    });
                    
                //    _logger.LogInformation("Firebase initialization successful");
                //}
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing Firebase: {Message}", ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.LogError("Inner exception: {InnerMessage}", ex.InnerException.Message);
                }
            }
        }

        // Helper method to check if a string is base64 encoded
        private bool IsBase64String(string s)
        {
            if (string.IsNullOrEmpty(s))
                return false;
                
            s = s.Trim();
            return (s.Length % 4 == 0) && Regex.IsMatch(s, @"^[a-zA-Z0-9\+/]*={0,3}$", RegexOptions.None);
        }

        public async Task<bool> BroadcastPushNotificationAsync(NotificationRequestModel notificationRequest)
        {
            try
            {
                var message = new Message()
                {
                    Topic = "TESTOTA",
                    Notification = new Notification()
                    {
                        Title = notificationRequest.Title,
                        Body = notificationRequest.Body
                    },
                    Data = notificationRequest.Data
                };

                string response = await FirebaseMessaging.DefaultInstance.SendAsync(message);

                _logger.LogInformation($"Successfully sent broadcast message: {response}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending broadcast message: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendPushNotificationAsync(NotificationRequestModel notificationRequest)
        {
            try
            {
                if(!string.IsNullOrWhiteSpace(notificationRequest.DeviceToken))
                {
                    var message = new Message()
                    {
                        Token = notificationRequest.DeviceToken,
                        Notification = new Notification()
                        {
                            Title = notificationRequest.Title,
                            Body = notificationRequest.Body
                        },
                        Data = notificationRequest.Data
                    };

                    string response = await FirebaseMessaging.DefaultInstance.SendAsync(message);
                    if (!string.IsNullOrWhiteSpace(response) && notificationRequest?.UserId > 0)
                    {
                        await _notificationRepository.AddUserNotificationDetails(new UserNotificationDetails() { UserId = (int)notificationRequest.UserId!, NotificationId = (int)notificationRequest.NotificationId!, 
                                                                                    Title = notificationRequest.Title!, Body = notificationRequest.Body! });
                    }
                    _logger.LogInformation($"Successfully sent message: {response}");
                    return true;
                }
                _logger.LogError($"Device token is null.");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending message: {ex.Message}");
                return false;
            }
        }

        public async Task<List<NotificationMaster>> GetNotificationDetailByCategory(string[] categories)
        {
            if (categories.Length == 0)
            {
                _logger.LogWarning("Category is null or empty in GetNotificationDetailByCategory.");
                return null;
            }

            try
            {
                var notification = await _notificationRepository.GetNotificationDetailByCategory(categories);

                if (notification == null)
                {
                    _logger.LogInformation($"No notification found for category: {categories}");
                    return null;
                }

                return notification;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving notification for category: {categories}");
                return null;
            }
        }

        public async Task<List<NotificationUserList>> GetUsersForNotificationBySRCId(string[] srcIds, string dataTypeName)
        {
            if (srcIds == null || srcIds.Length == 0)
            {
                _logger.LogWarning("srcIds array is null or empty in GetUsersForNotificationBySRCPropertyId.");
                return new List<NotificationUserList>();
            }

            try
            {
                var users = await _notificationRepository.GetUsersForNotificationBySRCId(srcIds, dataTypeName);
                if (users == null || users.Count == 0)
                {
                    _logger.LogInformation("No users found for the provided userIds in GetUsersForNotification.");
                    return null;
                }
                return users.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users for notification.");
                return new List<NotificationUserList>();
            }
        }
    }
}
