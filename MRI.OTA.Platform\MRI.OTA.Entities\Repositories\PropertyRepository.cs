﻿using Dapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Helper;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using System.Data;
using System.Text;
using static Dapper.SqlMapper;

namespace MRI.OTA.DBCore.Repositories
{
    /// <summary>
    /// Property repository
    /// </summary>
    public class PropertyRepository : BaseRepository<UserProperties, int>, IPropertyRepository
    {
        private readonly ILogger<PropertyRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;
        private readonly IAPITrackingRepository _apiTrackingRepository;

        /// <summary>
        /// Constructor for PropertyRepository
        /// </summary>
        /// <param name="dbConnection"></param>
        /// <param name="logger"></param>
        public PropertyRepository(IDbConnectionFactory dbConnection, ILogger<PropertyRepository> logger, IDapperWrapper dapperWrapper, IAPITrackingRepository apiTrackingRepository)
            : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
            _apiTrackingRepository = apiTrackingRepository;
        }

        /// <summary>
        /// Get all properties
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="offSet">The offset for pagination</param>
        /// <param name="limit">The limit for pagination</param>
        /// <param name="showAllRecords">If true, returns all records without pagination</param>
        /// <returns>List of user properties</returns>
        public async Task<List<ViewUserProperties>> GetAllProperties(int userId, int? offSet, int? limit, bool? showAllRecords = false)
        {
            offSet ??= 0;
            limit ??= 10;

            StringBuilder query = new StringBuilder();
            // SQL CTE
            query.Append($"WITH AllAgencyDetailCTE AS ( ");
            query.Append($"SELECT PropertyId, BusinessName AS AgencyName, BusinessRegisteredName, BusinessName, BusinessRegistrationNumber, Phone AS AgencyPhone, Email AS AgencyEmail, AgencyId, MriId, DarkLogoLink, LightLogoLink, BrandingBackgroundColor, CountryCode AS AgencyCountryCode, CountryName AS AgencyCountryName, StateCode AS AgencyStateCode, DataSourceId AS AgencyDataSourceId, ");
            query.Append($"StateName AS AgencyStateName, Suburb AS AgencySuburb, PostalCode AS AgencyPostalCode, AdministrativeArea AS AgencyAdministrativeArea, BuildingNumber AS AgencyBuildingNumber, LotNumber AS AgencyLotNumber, StreetAddress AS AgencyStreetAddress, City AS AgencyCity, Locale AS AgencyLocale, RuralDelivery AS AgencyRuralDelivery, PostOfficeName AS AgencyPostOfficeName, 0  AS IsActive ");
            query.Append($"FROM {Constants.ArchivePropertyAgencyDetailsTableName} ");
            query.Append($"UNION ALL ");
            query.Append($"SELECT NULL AS PropertyId, BusinessName AS AgencyName, BusinessRegisteredName, BusinessName, BusinessRegistrationNumber, Phone AS AgencyPhone, Email AS AgencyEmail, AgencyId, MriId, DarkLogoLink, LightLogoLink, BrandingBackgroundColor, CountryCode AS AgencyCountryCode, CountryName AS AgencyCountryName, StateCode AS AgencyStateCode, DataSourceId AS AgencyDataSourceId, ");
            query.Append($"StateName AS AgencyStateName, Suburb AS AgencySuburb, PostalCode AS AgencyPostalCode, AdministrativeArea AS AgencyAdministrativeArea, BuildingNumber AS AgencyBuildingNumber, LotNumber AS AgencyLotNumber, StreetAddress AS AgencyStreetAddress, City AS AgencyCity, Locale AS AgencyLocale, RuralDelivery AS AgencyRuralDelivery, PostOfficeName AS AgencyPostOfficeName, 1 AS IsActive ");
            query.Append($"FROM {Constants.AgencyDetailsTableName} ");
            query.Append($") ");
            // End

            query.Append($"SELECT UP.PropertyId,UP.UserId,UP.Bedrooms,UP.BathRooms, UP.CarSpaces, UP.FloorArea,UP.LandArea, 0 AS PurchasePrice,UP.IsActive,UP.OccupancyType,UP.Description,UP.PropertyType,UP.LotNumber,UP.OccupancyStatus,UP.PropertyRelationshipId,UP.PropertyName,UP.UserPropertiesNickNameId,UP.IsAddedToPortfolio,");
            query.Append($"UP.PropertyType ,PM.AuthorityStartDate AS OwnershipStartDate,PM.AuthorityEndDate AS OwnershipEndDate, PF.LeaseStart AS LeaseStartDate,PF.LeaseEnd AS LeaseEndDate,PF.Rent AS RentAmount,PR.PropertyRelationshipName,UN.PropertyNickName,UP.DefaultImageId,PI.ImageBlobUrl,");
            query.Append($"0 AS SecurityDeposit ,UP.Description, UP.StreetAddress, UP.Suburb,UP.City,CO.CountryName, UP.CountryCode,UP.StateID,ST.StateName,UP.AdministrativeArea,UP.Locale,UP.PostalCode,UP.PostOfficeName,UP.RuralDelivery,UP.SRCAgencyId,UP.SRCEntitytId,UP.SRCManagementId,UP.SRCTenancyId,UP.Currency, UP.Unit, UP.StreetNumber,");
            query.Append($"UP.UserDataSourceId,UD.AccessKey,DS.AccessSecret,UD.AccessToken,UD.DataSourceId,DS.ManifestJson,DS.Name,");
            query.Append($"PF.PropertyFinancialInformationId,PF.TenancyName,PF.LeaseStart,PF.LeaseEnd,PF.VacateDate,PF.Rent,PF.IncreaseRent,PF.IncreaseDate,PF.OptionsDate,PF.OptionsDetail,PF.Arrears,PF.PayToDate,PF.AmountToVacate,PF.OutstandingInvoices,PF.InvoiceFeesArrears,PF.WeeklyRent,PF.LastPaid,PF.HeldFunds,PF.RentCharge,");
            query.Append($"PF.PropertyOutstandingFees, PF.PropertyOutstandingInvoices, PM.PropertyManagerInformationId, PM.ManagementType, PM.PropertyManagerName, PM.PropertyManagerMobile, PM.PropertyManagerPhone, PM.PropertyManagerEmail, PM.AuthorityStartDate, PM.AuthorityEndDate, PM.Ownership, PM.ExpenditureLimit, PM.ExpenditureNotes, PM.ContactRole,");
            query.Append($"AD.AgencyName,AD.BusinessRegisteredName, AD.BusinessName, AD.BusinessRegistrationNumber, AD.AgencyPhone, AD.AgencyEmail, AD.AgencyId, AD.MriId, AD.DarkLogoLink, AD.LightLogoLink, AD.BrandingBackgroundColor, AD.AgencyCountryCode, AD.AgencyCountryName, AD.AgencyStateCode, AD.AgencyStateName, ");
            query.Append($"AD.AgencySuburb, AD.AgencyPostalCode, AD.AgencyAdministrativeArea, AD.AgencyBuildingNumber, AD.AgencyLotNumber,AD.AgencyStreetAddress, AD.AgencyCity, AD.AgencyLocale, AD.AgencyRuralDelivery, AD.AgencyPostOfficeName, AD.AgencyDataSourceId, DS.Name AS AgencyDataSourceName ");
            query.Append($" FROM  {Constants.UserPropertyTableName}  UP ");
            query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
            query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
            query.Append($"LEFT JOIN {Constants.PropertyFinancialInformationTableName} PF ON UP.PropertyId = PF.PropertyId ");
            query.Append($"LEFT JOIN {Constants.PropertyManagerInformationTableName} PM ON Up.PropertyId = PM.PropertyId ");
            query.Append($"LEFT JOIN {Constants.StateTableName} ST ON UP.StateID = ST.StateID ");
            query.Append($"LEFT JOIN {Constants.CountriesTableName} CO ON UP.CountryCode = CO.CountryCode ");
            query.Append($"LEFT JOIN {Constants.PropertyRelationshipTableName} PR ON PR.PropertyRelationshipId = UP.PropertyRelationshipId ");
            query.Append($"LEFT JOIN {Constants.UserPropertiesNickNameTableName} UN ON UN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId ");
            query.Append($"LEFT JOIN {Constants.PropertyImagesTableName} PI ON PI.PropertyImagesId = UP.DefaultImageId ");
            query.Append($"LEFT JOIN AllAgencyDetailCTE AD ON UP.SRCAgencyId = AD.AgencyId AND AD.IsActive = UP.IsActive AND UP.PropertyId = ISNULL(AD.PropertyId, UP.PropertyId) AND AD.AgencyDataSourceId = DS.DataSourceId ");
            query.Append($"WHERE UP.UserId = @UserId ORDER BY UP.PropertyId");

            // Only apply pagination if showAllRecords is false
            if (showAllRecords != true)
            {
                query.Append($" OFFSET @OffSet ROWS FETCH NEXT @Limit ROWS ONLY");
            }

            var parameters = new { UserId = userId, OffSet = offSet, Limit = limit };
            return await GetAllAsync<ViewUserProperties>(query.ToString(), parameters);
        }

        /// <summary>
        /// Get all images based on property IDs
        /// </summary>
        /// <param name="propertyIds"></param>
        /// <returns></returns>
        public async Task<List<PropertyImages>> GetPropertyImages(List<int> propertyIds)
        {
            if (propertyIds == null || !propertyIds.Any())
            {
                return new List<PropertyImages>();
            }

            StringBuilder query = new StringBuilder();
            query.Append($"SELECT PI.PropertyImagesId, PI.PropertyId, PI.ImageBlobUrl ");
            query.Append($"FROM {Constants.PropertyImagesTableName} PI ");
            query.Append($"WHERE PI.PropertyId IN @PropertyIds");

            var parameters = new DynamicParameters();
            parameters.Add("PropertyIds", propertyIds.ToArray());
            return await GetAllAsync<PropertyImages>(query.ToString(), parameters);
        }

        private async Task<int> CreateUserDataSource(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            var userDataSource = new UserDataSource
            {
                DataSourceId = userProperties.DataSourceId == 0
                    ? (int)Constants.DataSourceTypeEnum.SelfSource
                    : userProperties.DataSourceId
            };
            userProperties.DataSourceId = userDataSource.DataSourceId;
            var propUserDataSourceDic = ConvertToDictionary(userDataSource);
            var result = await AddAsync(Constants.UserDataSourceTableName, "UserDataSourceId", propUserDataSourceDic, transaction, connection);

            _logger.LogInformation("Created UserDataSource with ID {UserDataSourceId}", result);
            return result;
        }

        private async Task<int> CreateUserPropertiesNickName(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            if (userProperties.UserPropertiesNickNameId != null)
                return 0;

            // Check for duplicate nickname
            if (!string.IsNullOrWhiteSpace(userProperties.PropertyName))
            {
                string checkDuplicateQuery = $"SELECT COUNT(1) FROM {Constants.UserPropertiesNickNameTableName} WHERE UserId = @UserId AND PropertyNickName = @PropertyNickName AND IsActive = 1";
                var duplicateParams = new { UserId = userProperties.UserId, PropertyNickName = userProperties.PropertyName };
                var existingCount = await GetByIdAsync<int>(checkDuplicateQuery, duplicateParams, transaction, connection);
                
                if (existingCount > 0)
                {
                    _logger.LogWarning("Nickname '{Nickname}' already exists for user {UserId}", userProperties.PropertyName, userProperties.UserId);
                    return Constants.DuplicateError; // Return Constants.DuplicateError to indicate duplicate nickname error
                }
            }

            var userPropertiesNickName = new UserPropertiesNickName
            {
                UserId = userProperties.UserId,
                PropertyNickName = userProperties.PropertyName,
                IsActive = true
            };

            var userPropertiesNickNameDic = ConvertToDictionary(userPropertiesNickName);
            var result = await AddAsync(Constants.UserPropertiesNickNameTableName, "UserPropertiesNickNameId", userPropertiesNickNameDic, transaction, connection);

            _logger.LogInformation("Created UserPropertiesNickName with ID {NickNameId}", result);
            return result;
        }

        private async Task<int> CreateUserProperty(UserProperties userProperties, int userDataSourceId, int? userPropertiesNickNameId, IDbConnection connection, IDbTransaction transaction)
        {
            var propertyDic = ConvertToDictionary(userProperties);
            propertyDic["UserDataSourceId"] = userDataSourceId;
            if (userPropertiesNickNameId > 0)
            {
                propertyDic["UserPropertiesNickNameId"] = userPropertiesNickNameId;
            }

            var result = await AddAsync(Constants.UserPropertyTableName, "PropertyId", propertyDic, transaction, connection);
            _logger.LogInformation("Created Property with PropertyId {PropertyId}", result);
            return result;
        }

        private async Task<int> CreatePropertyImage(int propertyId, string ImageURL, IDbConnection connection, IDbTransaction transaction)
        {
            var propertyDic = new Dictionary<string, object>();
            propertyDic.Add("PropertyId", propertyId);
            propertyDic.Add("ImageBlobUrl", ImageURL);

            var result = await AddAsync(Constants.PropertyImagesTableName, "PropertyImagesId", propertyDic, transaction, connection);
            _logger.LogInformation("Image added with ID {PropertyImagesId}", result);
            return result;
        }

        private async Task CreateTenancyDetails(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                if (userProperties.TenanciesTenant == null) return;

                var propTenanciesDic = ConvertToDictionary(userProperties.TenanciesTenant);
                propTenanciesDic["PropertyId"] = userProperties.PropertyId;
                await AddAsync(Constants.PropertyFinancialInformationTableName, "PropertyFinancialInformationId", propTenanciesDic, transaction, connection);

                var propTenanciesPMDic = ConvertToDictionary(userProperties.TenanciesTenant.TenanciesPropertyManagerDetails);
                propTenanciesPMDic["PropertyId"] = userProperties.PropertyId;
                await AddAsync(Constants.PropertyManagerInformationTableName, "PropertyManagerInformationId", propTenanciesPMDic, transaction, connection);

                _logger.LogInformation("Created Tenancy details for PropertyId {PropertyId}", userProperties.PropertyId);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetTenanciesTenant,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Accept Invite Error in CreateTenancyDetails function for Property data: {System.Text.Json.JsonSerializer.Serialize(userProperties)}"
                });
                _logger.LogInformation("Error in creating tenancy details for PropertyId {PropertyId}", userProperties.PropertyId);
                return;
            }
        }

        private async Task CreateOwnerDetails(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                if (userProperties.TenanciesOwner == null) return;

                var propTenanciesDic = ConvertToDictionary(userProperties.TenanciesOwner);
                propTenanciesDic["PropertyId"] = userProperties.PropertyId;
                await AddAsync(Constants.PropertyFinancialInformationTableName, "PropertyFinancialInformationId", propTenanciesDic, transaction, connection);

                var propTenanciesPMDic = ConvertToDictionary(userProperties.TenanciesOwner.TenanciesPropertyManagerDetails);
                propTenanciesPMDic["PropertyId"] = userProperties.PropertyId;
                await AddAsync(Constants.PropertyManagerInformationTableName, "PropertyManagerInformationId", propTenanciesPMDic, transaction, connection);

                _logger.LogInformation("Created Owner details for PropertyId {PropertyId}", userProperties.PropertyId);
            }
            catch (Exception ex)
            {

                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetTenanciesOwner,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Accept Invite Error in CreateOwnerDetails function for Property data: {System.Text.Json.JsonSerializer.Serialize(userProperties)}"
                });
                _logger.LogInformation("Error in creating tenancy details for PropertyId {PropertyId}", userProperties.PropertyId);
                return;
            }
            
        }

        private async Task CreatePropertyDetails(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            await CreateDetailsList(userProperties.MaintenanceDetailList, userProperties, Constants.MaintenanceDetailsTableName, "MaintenanceDetailId", "maintenance", connection, transaction);
            await CreateDetailsList(userProperties.InspectionDetailList, userProperties, Constants.InspectionDetailsTableName, "InspectionsDetailId", "inspection", connection, transaction);
            await CreateDetailsList(userProperties.ComplianceDetailList, userProperties, Constants.ComplianceDetailsTableName, "ComplianceDetailId", "compliance", connection, transaction);
            await CreateDetailsList(userProperties.DocumentDetailList?.Where(x => !x.isRemove).ToList(), userProperties, Constants.DocumentDetailsTableName, "DocumentDetailId", "document", connection, transaction);
            await DeleteDocumentList(userProperties.DocumentDetailList?.Where(x => x.isRemove).ToList(), userProperties, Constants.DocumentDetailsTableName, "document", connection, transaction);
        }

        private async Task CreateDetailsList<T>(List<T> details, UserProperties userProperties, string tableName, string idColumnName, string detailType, IDbConnection connection, IDbTransaction transaction) where T : class
        {
            try
            {
                if (details?.Any() != true) return;

                foreach (var data in details)
                {
                    var propDic = ConvertToDictionary(data);
                    if (tableName != Constants.DocumentDetailsTableName) propDic["PropertyId"] = userProperties.PropertyId;
                    await AddAsync(tableName, idColumnName, propDic, transaction, connection);
                }

                _logger.LogInformation("Created {Count} {type} details for PropertyId {PropertyId}",
                    details.Count, detailType, userProperties.PropertyId);
            }
            catch (Exception ex)
            {
                int apiDetailId = tableName switch
                {
                    Constants.MaintenanceDetailsTableName => (int)Constants.APIDetail.GetMaintenance,
                    Constants.InspectionDetailsTableName => (int)Constants.APIDetail.GetInspections,
                    Constants.ComplianceDetailsTableName => (int)Constants.APIDetail.GetCompliance,
                    Constants.DocumentDetailsTableName => (int)Constants.APIDetail.GetDocuments,
                    _ => 0
                };
                
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = apiDetailId,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Accept Invite Error in CreateDetailsList function for table {tableName} for Property data:  {System.Text.Json.JsonSerializer.Serialize(details)}"
                });
                _logger.LogInformation("Error in creating data for table {TableName} for PropertyId {PropertyId}", tableName, userProperties.PropertyId);
                return;
            }
            

        }
        private async Task DeleteDocumentList(List<DocumentDetail> details, UserProperties userProperties, string tableName, string detailType, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                if (details?.Any() != true) return;

                foreach (var data in details)
                {
                    await DeleteAsync(tableName, data.SRCDocumentId.ToString(), "SRCDocumentId", transaction, connection);
                }

                _logger.LogInformation("Created {Count} {type} details for PropertyId {PropertyId}",
                    details.Count, detailType, userProperties.PropertyId);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetDocuments,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Accept Invite Error in Delete Document Process for PropertyID {userProperties.PropertyId} data:  {System.Text.Json.JsonSerializer.Serialize(details)}"
                });
                _logger.LogInformation("Error in Delete Document Process for PropertyId {PropertyId}", userProperties.PropertyId);
                return;
            }
        }

        private async Task CreatePropertyFinancialAndManagerInformation(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                if (userProperties.PropertyManagerInformation != null)
                {
                    var propManagerDic = ConvertToDictionary(userProperties.PropertyManagerInformation);
                    propManagerDic["PropertyId"] = userProperties.PropertyId;
                    await AddAsync(Constants.PropertyManagerInformationTableName, "PropertyManagerInformationId", propManagerDic, transaction, connection);
                    _logger.LogInformation("Created property manager information for PropertyId {PropertyId}", userProperties.PropertyId);
                }

                if (userProperties.PropertyFinancialInformation != null)
                {
                    var propFinancialDic = ConvertToDictionary(userProperties.PropertyFinancialInformation);
                    propFinancialDic["PropertyId"] = userProperties.PropertyId;
                    await AddAsync(Constants.PropertyFinancialInformationTableName, "PropertyFinancialInformationId", propFinancialDic, transaction, connection);
                    _logger.LogInformation("Created property financial information for PropertyId {PropertyId}", userProperties.PropertyId);
                }
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetManagement,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Error in CreatePropertyFinancialAndManagerInformation function for PropertyID {userProperties.PropertyId} and data:  {System.Text.Json.JsonSerializer.Serialize(userProperties)}"
                });
                _logger.LogInformation("Error in CreatePropertyFinancialAndManagerInformation function for PropertyId {PropertyId}", userProperties.PropertyId);
                return;
            }
            
        }
        private async Task CreateArchivePropertyAgencyInformation(UserProperties userProperties, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(userProperties.SRCAgencyId) && (!userProperties.IsActive))
                {
                    var archiveAgencyData = await GetAgencyDetails([userProperties.SRCAgencyId]);
                    if(archiveAgencyData != null && archiveAgencyData.Any())
                    {
                        archiveAgencyData[0].PropertyId = userProperties.PropertyId;
                        archiveAgencyData[0].SRCPropertyId = userProperties.SRCEntitytId;
                        var propDic = ConvertToDictionary(archiveAgencyData[0]);
                        await AddAsync(Constants.ArchivePropertyAgencyDetailsTableName, "ArchivePropertyAgencyDetailId", propDic, transaction, connection);
                        _logger.LogInformation("Created Archive Agency information for PropertyId {PropertyId} and SRCAgencyId {SRCAgencyId}", userProperties.PropertyId, userProperties.SRCAgencyId);
                    } else
                    {
                        _logger.LogWarning("No agency data found for SRCAgencyId {SRCAgencyId} for PropertyId {PropertyId}", userProperties.SRCAgencyId, userProperties.PropertyId);
                    }
                }
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetManagement,
                    InviteCode = null!,
                    UserId = userProperties.UserId,
                    IsCompleted = false,
                    ErrorInfo = ex.Message,
                    AdditionalErrorInfo = $"Error in CreatePropertyFinancialAndManagerInformation function for PropertyID {userProperties.PropertyId} and data:  {System.Text.Json.JsonSerializer.Serialize(userProperties)}"
                });
                _logger.LogInformation("Error in CreatePropertyFinancialAndManagerInformation function for PropertyId {PropertyId}", userProperties.PropertyId);
                return;
            }

        }

        private async Task<int> UpdatePropertyDefaultImageId(int propertyId, int propertyImageId, IDbConnection connection, IDbTransaction transaction)
        {
            var query = @"UPDATE UserProperties SET DefaultImageId = @propertyImageId WHERE PropertyId = @PropertyId";
            var parameters = new { propertyImageId = propertyImageId, PropertyId = propertyId };

            var result = await this.UpdateAsync(query, parameters, transaction, connection);
            _logger.LogInformation("Updated DefaultImageId for {PropertyId}", propertyId);
            return result;
        }
        /// <summary>
        /// Add property
        /// </summary>
        /// <param name="userProperties">The user properties to add</param>
        /// <returns>The ID of the newly created property</returns>
        public async Task<int> AddProperty(UserProperties userProperties)
        {
            return await ExecuteWithTransactionAsync<int>(async (connection, transaction) =>
            {
                try
                {
                    int userPropertiesNickNameId = 0;
                    // Step 1: Create UserDataSource
                    var userDataSourceId = await CreateUserDataSource(userProperties, connection, transaction);

                    // If data is coming from self source then insert in NickName Table
                    if (userProperties.DataSourceId == (int)Constants.DataSourceTypeEnum.SelfSource)
                    {
                        // Step 2: Create UserPropertiesNickName if needed
                        userPropertiesNickNameId = await CreateUserPropertiesNickName(userProperties, connection, transaction);
                        
                        // Check if nickname creation failed due to duplicate
                        if (userPropertiesNickNameId == Constants.DuplicateError)
                        {
                            _logger.LogWarning("Cannot add property due to duplicate nickname '{PropertyName}' for user {UserId}", userProperties.PropertyName, userProperties.UserId);
                            return Constants.DuplicateError;
                        }
                        
                        userProperties.IsAddedToPortfolio = true; // Set IsAddedToPortfolio to true for self source
                    }

                    // Step 3: Create main property record
                    var propertyId = await CreateUserProperty(userProperties, userDataSourceId, userPropertiesNickNameId, connection, transaction);
                    userProperties.PropertyId = propertyId;
                    if (userProperties.DataSourceId == (int)Constants.DataSourceTypeEnum.SelfSource)
                    {
                        // Step 3: Create property information (manager and financial)
                        await CreatePropertyFinancialAndManagerInformation(userProperties, connection, transaction);
                    } else
                    {
                        await CreateArchivePropertyAgencyInformation(userProperties, connection, transaction);
                    }

                    if (userProperties.DataSourceId != (int)Constants.DataSourceTypeEnum.SelfSource && !string.IsNullOrEmpty(userProperties.DefaultImageLink))
                    {
                        // Step 4: Add Default Image and Update UserProperties
                        var propertyImageId = await CreatePropertyImage(propertyId, userProperties.DefaultImageLink, connection, transaction);

                        // Step 5: update Default Image id in UserProperties
                        await UpdatePropertyDefaultImageId(propertyId, propertyImageId, connection, transaction);
                    }


                    _logger.LogInformation("Successfully added property {PropertyName} with ID {PropertyId}", userProperties.PropertyName, propertyId);
                    return propertyId > 0 ? propertyId : Constants.Error;
                }
                catch (Exception ex)
                {
                    await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = (int)Constants.APIDetail.GetProperties,
                        InviteCode = null!,
                        UserId = userProperties.UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message,
                        AdditionalErrorInfo = $"Error in AddProperty function for PropertyID {userProperties.PropertyId} and data:  {System.Text.Json.JsonSerializer.Serialize(userProperties)}"
                    });
                    _logger.LogError(ex, "Error adding property {PropertyName}", userProperties.PropertyName);
                    throw;
                }
            });
        }
        public async Task<int> AddPropertyOtherDetails(UserProperties userProperties)
        {
            return await ExecuteWithTransactionAsync<int>(async (connection, transaction) =>
            {
                try
                {
                    // Step 1: Create tenancy and owner details
                    await CreateTenancyDetails(userProperties, connection, transaction);
                    await CreateOwnerDetails(userProperties, connection, transaction);

                    // Step 2: Create additional property details (maintenance, inspection, compliance, documents)
                    await CreatePropertyDetails(userProperties, connection, transaction);

                    // Step 3: Create property information (manager and financial)
                    await CreatePropertyFinancialAndManagerInformation(userProperties, connection, transaction);

                    _logger.LogInformation("Successfully added property data {PropertyName} with ID {PropertyId}", userProperties.PropertyName, userProperties.PropertyId);
                    return Constants.Success;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error adding property data {PropertyName}", userProperties.PropertyName);
                    throw;
                }
            });
        }

        /// <summary>
        /// Update property
        /// </summary>
        /// <param name="userProperties"></param>
        /// <returns></returns>
        public async Task<int> UpdateProperty(UserProperties userProperties)
        {
            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                try
                {
                    int totalRowsAffected = 0;
                    // Update PropertyManagerInformation
                    if (userProperties.PropertyManagerInformation != null)
                    {
                        var propManagersDic = ConvertToDictionary(userProperties.PropertyManagerInformation);
                        string updatePropertyManagerSql = await GenerateUpdateStatement(
                            Constants.PropertyManagerInformationTableName,
                            propManagersDic,
                            $"PropertyId = @PropertyId");

                        var managerParams = new DynamicParameters(propManagersDic);
                        managerParams.Add("PropertyId", userProperties.PropertyId);
                        var managerResult = await ExecuteAsync(updatePropertyManagerSql, managerParams, transaction, connection);
                        totalRowsAffected += managerResult;
                    }

                    // Update PropertyFinancialInformation
                    var propFinancialDic = ConvertToDictionary(userProperties.PropertyFinancialInformation);
                    string updatePropertyFinancialSql = await GenerateUpdateStatement(
                        Constants.PropertyFinancialInformationTableName,
                        propFinancialDic,
                        $"PropertyId = @PropertyId");

                    var financialParams = new DynamicParameters(propFinancialDic);
                    financialParams.Add("PropertyId", userProperties.PropertyId);
                    var financialResult = await ExecuteAsync(updatePropertyFinancialSql, financialParams, transaction, connection);
                    totalRowsAffected += financialResult;

                    // Update UserProperties
                    var propertyDic = ConvertToDictionary(userProperties);
                    propertyDic.Remove("IsAddedToPortfolio");
                    string updatePropertySql = await GenerateUpdateStatement(
                        Constants.UserPropertyTableName,
                        propertyDic,
                        $"PropertyId = @PropertyId");

                    var propertyParams = new DynamicParameters(propertyDic);
                    propertyParams.Add("PropertyId", userProperties.PropertyId);
                    var propertyResult = await ExecuteAsync(updatePropertySql, propertyParams, transaction, connection);
                    totalRowsAffected += propertyResult;

                    return totalRowsAffected > 0 ? userProperties.PropertyId : Constants.Error;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error updating property: {ex.Message}");
                    throw;
                }
            });
        }

        /// <summary>
        /// Get property by Id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<ViewUserProperties> GetPropertyById(int userId, int propertyId)
        {
            StringBuilder query = new StringBuilder();
            // SQL CTE
            query.Append($"WITH AllAgencyDetailCTE AS ( ");
            query.Append($"SELECT PropertyId, BusinessName AS AgencyName, BusinessRegisteredName, BusinessName, BusinessRegistrationNumber, Phone AS AgencyPhone, Email AS AgencyEmail, AgencyId, MriId, DarkLogoLink, LightLogoLink, BrandingBackgroundColor, CountryCode AS AgencyCountryCode, CountryName AS AgencyCountryName, StateCode AS AgencyStateCode, DataSourceId AS AgencyDataSourceId, ");
            query.Append($"StateName AS AgencyStateName, Suburb AS AgencySuburb, PostalCode AS AgencyPostalCode, AdministrativeArea AS AgencyAdministrativeArea, BuildingNumber AS AgencyBuildingNumber, LotNumber AS AgencyLotNumber, StreetAddress AS AgencyStreetAddress, City AS AgencyCity, Locale AS AgencyLocale, RuralDelivery AS AgencyRuralDelivery, PostOfficeName AS AgencyPostOfficeName, 0  AS IsActive ");
            query.Append($"FROM {Constants.ArchivePropertyAgencyDetailsTableName} ");
            query.Append($"UNION ALL ");
            query.Append($"SELECT NULL AS PropertyId, BusinessName AS AgencyName, BusinessRegisteredName, BusinessName, BusinessRegistrationNumber, Phone AS AgencyPhone, Email AS AgencyEmail, AgencyId, MriId, DarkLogoLink, LightLogoLink, BrandingBackgroundColor, CountryCode AS AgencyCountryCode, CountryName AS AgencyCountryName, StateCode AS AgencyStateCode, DataSourceId AS AgencyDataSourceId, ");
            query.Append($"StateName AS AgencyStateName, Suburb AS AgencySuburb, PostalCode AS AgencyPostalCode, AdministrativeArea AS AgencyAdministrativeArea, BuildingNumber AS AgencyBuildingNumber, LotNumber AS AgencyLotNumber, StreetAddress AS AgencyStreetAddress, City AS AgencyCity, Locale AS AgencyLocale, RuralDelivery AS AgencyRuralDelivery, PostOfficeName AS AgencyPostOfficeName, 1 AS IsActive ");
            query.Append($"FROM {Constants.AgencyDetailsTableName} ");
            query.Append($") ");
            // End

            query.Append($"SELECT UP.PropertyId,UP.UserId,UP.Bedrooms,UP.BathRooms, UP.CarSpaces, UP.FloorArea,UP.LandArea, 0 AS PurchasePrice,UP.IsActive,UP.OccupancyType,UP.Description,UP.PropertyType,UP.LotNumber,UP.OccupancyStatus,UP.PropertyRelationshipId,UP.PropertyName,UP.UserPropertiesNickNameId,UP.IsAddedToPortfolio,");
            query.Append($"UP.PropertyType ,PM.AuthorityStartDate AS OwnershipStartDate,PM.AuthorityEndDate AS OwnershipEndDate, PF.LeaseStart AS LeaseStartDate,PF.LeaseEnd AS LeaseEndDate,PF.Rent AS RentAmount,PR.PropertyRelationshipName,UN.PropertyNickName,UP.DefaultImageId,PI.ImageBlobUrl,");
            query.Append($"0 AS SecurityDeposit ,UP.Description, UP.StreetAddress, UP.Suburb,UP.City,CO.CountryName, UP.CountryCode,UP.StateID,ST.StateName,UP.AdministrativeArea,UP.Locale,UP.PostalCode,UP.PostOfficeName,UP.RuralDelivery,UP.SRCAgencyId,UP.SRCEntitytId,UP.SRCManagementId,UP.SRCTenancyId,UP.Currency, UP.Unit, UP.StreetNumber,");
            query.Append($"UP.UserDataSourceId,UD.AccessKey,DS.AccessSecret,UD.AccessToken,UD.DataSourceId,DS.ManifestJson,DS.Name,");
            query.Append($"PF.PropertyFinancialInformationId,PF.TenancyName,PF.LeaseStart,PF.LeaseEnd,PF.VacateDate,PF.Rent,PF.IncreaseRent,PF.IncreaseDate,PF.OptionsDate,PF.OptionsDetail,PF.Arrears,PF.PayToDate,PF.AmountToVacate,PF.OutstandingInvoices,PF.InvoiceFeesArrears,PF.WeeklyRent,PF.LastPaid,PF.HeldFunds,PF.RentCharge,");
            query.Append($"PF.PropertyOutstandingFees, PF.PropertyOutstandingInvoices, PM.PropertyManagerInformationId, PM.ManagementType, PM.PropertyManagerName, PM.PropertyManagerMobile, PM.PropertyManagerPhone, PM.PropertyManagerEmail, PM.AuthorityStartDate, PM.AuthorityEndDate, PM.Ownership, PM.ExpenditureLimit, PM.ExpenditureNotes, PM.ContactRole,");
            query.Append($"AD.AgencyName,AD.BusinessRegisteredName, AD.BusinessName, AD.BusinessRegistrationNumber, AD.AgencyPhone, AD.AgencyEmail, AD.AgencyId, AD.MriId, AD.DarkLogoLink, AD.LightLogoLink, AD.BrandingBackgroundColor, AD.AgencyCountryCode, AD.AgencyCountryName, AD.AgencyStateCode, AD.AgencyStateName, ");
            query.Append($"AD.AgencySuburb, AD.AgencyPostalCode, AD.AgencyAdministrativeArea, AD.AgencyBuildingNumber, AD.AgencyLotNumber,AD.AgencyStreetAddress, AD.AgencyCity, AD.AgencyLocale, AD.AgencyRuralDelivery, AD.AgencyPostOfficeName, AD.AgencyDataSourceId, DS.Name AS AgencyDataSourceName ");
            query.Append($" FROM  {Constants.UserPropertyTableName}  UP ");
            query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
            query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
            query.Append($"LEFT JOIN {Constants.PropertyFinancialInformationTableName} PF ON UP.PropertyId = PF.PropertyId ");
            query.Append($"LEFT JOIN {Constants.PropertyManagerInformationTableName} PM ON Up.PropertyId = PM.PropertyId ");
            query.Append($"LEFT JOIN {Constants.StateTableName} ST ON UP.StateID = ST.StateID ");
            query.Append($"LEFT JOIN {Constants.CountriesTableName} CO ON UP.CountryCode = CO.CountryCode ");
            query.Append($"LEFT JOIN {Constants.PropertyRelationshipTableName} PR ON PR.PropertyRelationshipId = UP.PropertyRelationshipId ");
            query.Append($"LEFT JOIN {Constants.UserPropertiesNickNameTableName} UN ON UN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId ");
            query.Append($"LEFT JOIN {Constants.PropertyImagesTableName} PI ON PI.PropertyImagesId = UP.DefaultImageId ");
            query.Append($"LEFT JOIN AllAgencyDetailCTE AD ON UP.SRCAgencyId = AD.AgencyId AND AD.IsActive = UP.IsActive AND UP.PropertyId = ISNULL(AD.PropertyId, UP.PropertyId) AND AD.AgencyDataSourceId = DS.DataSourceId ");
            query.Append($"WHERE UP.UserId = @UserId AND UP.PropertyId = @PropertyId");

            var parameters = new { UserId = userId, PropertyId = propertyId };
            return await GetByIdAsync<ViewUserProperties>(query.ToString(), parameters);
        }

        /// <summary>
        /// Get property relations
        /// </summary>
        /// <param name="userPropertiesNickNameId"></param>
        /// <returns></returns>
        public async Task<List<ViewUserPropertiesNickName>> GetPropertyRelations(int userPropertiesNickNameId)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT UP.PropertyId,UP.UserId,UP.PropertyRelationshipId,UP.PropertyName,UP.UserPropertiesNickNameId,PR.PropertyRelationshipName,UPN.IsActive, UP.UserPropertiesNickNameId");
            query.Append($" FROM {Constants.UserPropertyTableName} UP  LEFT JOIN {Constants.PropertyRelationshipTableName} PR ON UP.PropertyRelationshipId = PR.PropertyRelationshipId ");
            query.Append($"LEFT JOIN {Constants.UserPropertiesNickNameTableName} UPN ON UPN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId ");
            query.Append($" WHERE UP.UserPropertiesNickNameId = @UserPropertiesNickNameId");

            var parameters = new { UserPropertiesNickNameId = userPropertiesNickNameId };
            return await GetByIdListAsync<ViewUserPropertiesNickName>(query.ToString(), parameters);
        }

        /// <summary>
        /// Get property relations by nick name
        /// </summary>
        /// <param name="userPropertiesNickNameId"></param>
        /// <returns></returns>
        public async Task<List<ViewUserPropertiesNickName>> GetPropertyNickNames(int userId)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT UPN.IsActive, UPN.UserPropertiesNickNameId,UPN.UserId,UPN.UserPropertiesNickNameId,UPN.PropertyNickName");
            query.Append($" FROM {Constants.UserPropertiesNickNameTableName} UPN ");
            query.Append($" WHERE UserId = @UserId");

            var parameters = new { UserId = userId };
            return await GetByIdListAsync<ViewUserPropertiesNickName>(query.ToString(), parameters);
        }

        /// <summary>
        /// Delete property
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public Task<int> DeleteProperty(int propertyId)
        {
            string query = $"UPDATE {Constants.UserPropertyTableName} SET IsActive = 0 WHERE PropertyId = @PropertyId;";
            var parameters = new { PropertyId = propertyId };
            return UpdateAsync(query, parameters);
        }

        /// <summary>
        /// Update property status
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public Task<int> UpdatePropertyStatus(int userId, int propertyId, bool isActive)
        {
            string query = $"UPDATE {Constants.UserPropertyTableName} SET IsActive = @IsActive WHERE PropertyId = @PropertyId AND UserId = @UserId;";
            var parameters = new { PropertyId = propertyId, IsActive = isActive, UserId = userId };
            return UpdateAsync(query, parameters);
        }

        /// <summary>
        /// Update property portfolio - creates or updates property-nickname relationships
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="propertyId">Property ID</param>
        /// <param name="nicknameId">Nickname ID - if 0, create new entry; if greater than 0, update existing entry</param>
        /// <param name="nickname">Nickname for the property</param>
        /// <returns>Returns affected rows count. Returns Constants.DuplicateError if nickname already exists.</returns>
        public async Task<int> UpdatePropertyPortfolio(int userId, int propertyId, int nicknameId, string? nickname)
        {
            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                try
                {
                    int result = 0;
                    int userPropertiesNickNameId = nicknameId;

                    // If nicknameId is 0, create new entry in UserPropertiesNickName
                    if (nicknameId == 0)
                    {
                        // Check if nickname already exists for this user
                        if (!string.IsNullOrWhiteSpace(nickname))
                        {
                            string checkDuplicateQuery = $"SELECT COUNT(1) FROM {Constants.UserPropertiesNickNameTableName} WHERE UserId = @UserId AND PropertyNickName = @PropertyNickName AND IsActive = 1";
                            var duplicateParams = new { UserId = userId, PropertyNickName = nickname };
                            var existingCount = await GetByIdAsync<int>(checkDuplicateQuery, duplicateParams, transaction, connection);

                            if (existingCount > 0)
                            {
                                _logger.LogWarning("Nickname '{Nickname}' already exists for user {UserId}", nickname, userId);
                                return Constants.DuplicateError; // Return Constants.DuplicateError to indicate duplicate nickname error
                            }
                        }

                        var userPropertiesNickName = new UserPropertiesNickName
                        {
                            UserId = userId,
                            PropertyNickName = nickname,
                            IsActive = true
                        };

                        var userPropertiesNickNameDic = ConvertToDictionary(userPropertiesNickName);
                        userPropertiesNickNameId = await AddAsync(Constants.UserPropertiesNickNameTableName, "UserPropertiesNickNameId", userPropertiesNickNameDic, transaction, connection);

                        _logger.LogInformation("Created new UserPropertiesNickName with ID {NickNameId} for user {UserId}", userPropertiesNickNameId, userId);
                    }

                    // Update the UserProperties table with the UserPropertiesNickNameId
                    string updatePropertyQuery = $"UPDATE {Constants.UserPropertyTableName} SET UserPropertiesNickNameId = @UserPropertiesNickNameId , IsAddedToPortfolio = 1 WHERE PropertyId = @PropertyId AND UserId = @UserId;";
                    var propertyParams = new { UserPropertiesNickNameId = userPropertiesNickNameId, PropertyId = propertyId, UserId = userId };
                    result = await ExecuteAsync(updatePropertyQuery, propertyParams, transaction, connection);

                    _logger.LogInformation("Updated property {PropertyId} with UserPropertiesNickNameId {NickNameId} for user {UserId}", propertyId, userPropertiesNickNameId, userId);

                    return result > 0 ? Constants.Success : Constants.Error;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating property portfolio for user {UserId}, property {PropertyId}, nickname {NicknameId}", userId, propertyId, nicknameId);
                    throw;
                }
            });
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by data source
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of tuples with data source info and counts</returns>
        public async Task<List<(int DataSourceId, string DataSourceName, int ActiveCount, int InactiveCount)>> GetPropertyCountsByDataSource(int userId)
        {
            try
            {
                string query = @$"
            SELECT
                DS.DataSourceId,
                DS.Name AS DataSourceName,
                SUM(CASE WHEN UP.IsActive = 1 THEN 1 ELSE 0 END) AS ActiveCount,
                SUM(CASE WHEN UP.IsActive = 0 THEN 1 ELSE 0 END) AS InactiveCount
            FROM {Constants.UserPropertyTableName} UP
            INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId
            INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId
            WHERE UP.UserId = @UserId
            GROUP BY DS.DataSourceId, DS.Name";

                var parameters = new { UserId = userId };

                var results = await QueryAsync<(int DataSourceId, string DataSourceName, int ActiveCount, int InactiveCount)>(query, parameters);

                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property counts by data source for user {UserId}", userId);
                return new List<(int DataSourceId, string DataSourceName, int ActiveCount, int InactiveCount)>();
            }
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by agency ID with comprehensive agency information
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of agency count models with comprehensive agency information</returns>
        public async Task<List<ViewAgencyPropertyCount>> GetPropertyCountsByAgency(int userId)
        {
            try
            {
                string query = @$"
                    -- Self-source properties (AgencyId is NULL)
                    SELECT
                        NULL AS AgencyId,
                        NULL AS AgencyName,
                        NULL AS BusinessRegisteredName,
                        NULL AS BusinessName,
                        NULL AS BusinessRegistrationNumber,
                        NULL AS Phone,
                        NULL AS Email,
                        NULL AS MriId,
                        NULL AS DarkLogoLink,
                        NULL AS LightLogoLink,
                        NULL AS BrandingBackgroundColor,
                        NULL AS CountryCode,
                        NULL AS CountryName,
                        NULL AS StateCode,
                        NULL AS StateName,
                        NULL AS Suburb,
                        NULL AS PostalCode,
                        NULL AS AdministrativeArea,
                        NULL AS BuildingNumber,
                        NULL AS LotNumber,
                        NULL AS StreetAddress,
                        NULL AS City,
                        NULL AS Locale,
                        NULL AS RuralDelivery,
                        NULL AS PostOfficeName,
                        NULL AS DataSourceId,
                        NULL AS DataSourceName,
                        SUM(CASE WHEN UP.IsActive = 1 THEN 1 ELSE 0 END) AS ActiveCount,
                        SUM(CASE WHEN UP.IsActive = 0 THEN 1 ELSE 0 END) AS InactiveCount
                    FROM {Constants.UserPropertyTableName} UP
                    WHERE UP.UserId = @UserId AND UP.SRCAgencyId IS NULL
                    
                    UNION ALL
                    
                    -- Agency properties (AgencyId is NOT NULL)
                    SELECT
                        UPS.SRCAgencyId AS AgencyId,
                        AD.BusinessName AS AgencyName,
                        AD.BusinessRegisteredName,
                        AD.BusinessName,
                        AD.BusinessRegistrationNumber,
                        AD.Phone,
                        AD.Email,
                        AD.MriId,
                        AD.DarkLogoLink,
                        AD.LightLogoLink,
                        AD.BrandingBackgroundColor,
                        AD.CountryCode,
                        AD.CountryName,
                        AD.StateCode,
                        AD.StateName,
                        AD.Suburb,
                        AD.PostalCode,
                        AD.AdministrativeArea,
                        AD.BuildingNumber,
                        AD.LotNumber,
                        AD.StreetAddress,
                        AD.City,
                        AD.Locale,
                        AD.RuralDelivery,
                        AD.PostOfficeName,
                        AD.DataSourceId,
                        DS.Name AS DataSourceName,
                        UPS.ActiveCount,
                        UPS.InactiveCount
                    FROM {Constants.AgencyDetailsTableName} AD 
					INNER JOIN (SELECT SUM(CASE WHEN UP.IsActive = 1 THEN 1 ELSE 0 END) AS ActiveCount,SUM(CASE WHEN UP.IsActive = 0 THEN 1 ELSE 0 END) AS InactiveCount, UP.SRCAgencyId , UP.UserId, UD.DataSourceId
									FROM {Constants.UserPropertyTableName} UP INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId
									WHERE UP.UserId = @UserId AND UP.SRCAgencyId IS NOT NULL GROUP BY UP.SRCAgencyId, UP.UserId, UD.DataSourceId
								) UPS ON UPS.SRCAgencyId = AD.AgencyId AND UPS.DataSourceId = AD.DataSourceId
					INNER JOIN {Constants.DataSourceTableName} DS ON DS.DataSourceId = UPS.DataSourceId";

                var parameters = new { UserId = userId };

                var results = await QueryAsync<ViewAgencyPropertyCount>(query, parameters);

                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property counts by agency for user {UserId}", userId);
                return new List<ViewAgencyPropertyCount>();
            }
        }

        /// <summary>
        /// Get properties by multiple user IDs
        /// </summary>
        /// <param name="userId">Array of user IDs</param>
        /// <returns>List of user properties</returns>
        public async Task<List<UserProperties>> GetPropertiesByUserIds(string[] userId)
        {
            if (userId == null || !userId.Any())
            {
                return new List<UserProperties>();
            }

            StringBuilder query = new StringBuilder();
            query.Append($"SELECT UP.PropertyId, UP.UserId, UP.SRCEntitytId, UP.SRCAgencyId, UP.SRCManagementId, UP.ProviderId, ");
            query.Append($"UP.Bedrooms, UP.BathRooms, UP.CarSpaces, UP.FloorArea, UP.LandArea, ");
            query.Append($"UP.IsActive, UP.OccupancyType, UP.Description, UP.PropertyType, UP.LotNumber, UP.OccupancyStatus, ");
            query.Append($"UP.PropertyRelationshipId, UP.PropertyName, UP.UserPropertiesNickNameId, UP.DefaultImageId, ");
            query.Append($"UP.StreetAddress, UP.Suburb, UP.City, UP.CountryCode, UP.StateID, UP.AdministrativeArea, ");
            query.Append($"UP.Locale, UP.PostalCode, UP.PostOfficeName, UP.RuralDelivery, UP.UserDataSourceId, UP.DataSourceId ");
            query.Append($"FROM {Constants.UserPropertyTableName} UP ");
            query.Append($"INNER JOIN {Constants.UserTableName} U ON UP.UserId = U.UserId ");
            query.Append($"WHERE U.ProviderId IN @UserIds AND UP.IsActive = 1 ");
            query.Append($"ORDER BY UP.UserId, UP.PropertyId");

            var parameters = new DynamicParameters();
            parameters.Add("UserIds", userId);

            return await GetAllAsync<UserProperties>(query.ToString(), parameters);
        }

        /// <summary>
        /// Bulk upsert user properties using MERGE statement based on SRCEntitytId - insert if not exists, update if exists
        /// </summary>
        /// <param name="userPropertiesList">List of user properties to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public async Task<List<SQLQueryMergeResult>> BulkUpsertUserProperties(List<UserProperties> userPropertiesList, int dataSourceId)
        {
            if (userPropertiesList == null || !userPropertiesList.Any())
            {
                return null;
            }

            return await ExecuteWithTransactionAsync<List<SQLQueryMergeResult>>(async (connection, transaction) =>
            {
                try
                {
                    // Create DataTable to hold properties data
                    var dataTable = new DataTable();
                    dataTable.Columns.Add("UserId", typeof(int));
                    dataTable.Columns.Add("SRCEntitytId", typeof(string));
                    dataTable.Columns.Add("SRCAgencyId", typeof(string));
                    dataTable.Columns.Add("SRCManagementId", typeof(string));
                    dataTable.Columns.Add("SRCTenancyId", typeof(string));
                    dataTable.Columns.Add("ProviderId", typeof(string));
                    dataTable.Columns.Add("PropertyName", typeof(string));
                    dataTable.Columns.Add("StreetAddress", typeof(string));
                    dataTable.Columns.Add("Suburb", typeof(string));
                    dataTable.Columns.Add("City", typeof(string));
                    dataTable.Columns.Add("StateID", typeof(string));
                    dataTable.Columns.Add("CountryCode", typeof(string));
                    dataTable.Columns.Add("PostalCode", typeof(string));
                    dataTable.Columns.Add("PropertyType", typeof(string));
                    dataTable.Columns.Add("Bedrooms", typeof(int));
                    dataTable.Columns.Add("BathRooms", typeof(int));
                    dataTable.Columns.Add("CarSpaces", typeof(int));
                    dataTable.Columns.Add("FloorArea", typeof(int));
                    dataTable.Columns.Add("LandArea", typeof(int));
                    dataTable.Columns.Add("Description", typeof(string));
                    dataTable.Columns.Add("OccupancyType", typeof(string));
                    dataTable.Columns.Add("OccupancyStatus", typeof(string));
                    dataTable.Columns.Add("PropertyRelationshipId", typeof(int));
                    dataTable.Columns.Add("LotNumber", typeof(string));
                    dataTable.Columns.Add("AdministrativeArea", typeof(string));
                    dataTable.Columns.Add("Locale", typeof(string));
                    dataTable.Columns.Add("PostOfficeName", typeof(string));
                    dataTable.Columns.Add("RuralDelivery", typeof(string)); 
                    dataTable.Columns.Add("IsActive", typeof(bool));
                    dataTable.Columns.Add("UserDataSourceId", typeof(int));
                    dataTable.Columns.Add("BuildingNumber", typeof(string));
                    dataTable.Columns.Add("Unit", typeof(string));
                    dataTable.Columns.Add("StreetNumber", typeof(string));

                    // Process each property to create UserDataSource and populate DataTable
                    foreach (var property in userPropertiesList)
                    {
                        // Create UserDataSource for each property (following AddProperty pattern)
                        var userDataSource = new UserDataSource
                        {
                            DataSourceId = property.DataSourceId == 0
                                ? (int)Constants.DataSourceTypeEnum.SelfSource
                                : property.DataSourceId
                        };

                        var propUserDataSourceDic = ConvertToDictionary(userDataSource);
                        var userDataSourceId = await AddAsync(Constants.UserDataSourceTableName, "UserDataSourceId", propUserDataSourceDic, transaction, connection);

                        // Add row to DataTable
                        dataTable.Rows.Add(
                            property.UserId,
                            property.SRCEntitytId ?? (object)DBNull.Value,
                            property.SRCAgencyId ?? (object)DBNull.Value,
                            property.SRCManagementId ?? (object)DBNull.Value,
                            property.SRCTenancyId ?? (object)DBNull.Value,
                            property.ProviderId ?? (object)DBNull.Value,
                            property.PropertyName ?? (object)DBNull.Value,
                            property.StreetAddress ?? (object)DBNull.Value,
                            property.Suburb ?? (object)DBNull.Value,
                            property.City ?? (object)DBNull.Value,
                            property.StateId ?? (object)DBNull.Value,
                            property.CountryCode ?? (object)DBNull.Value,
                            property.PostalCode ?? (object)DBNull.Value,
                            property.PropertyType ?? (object)DBNull.Value,
                            property.Bedrooms == 0 ? (object)DBNull.Value : property.Bedrooms,
                            property.BathRooms == 0 ? (object)DBNull.Value : property.BathRooms,
                            property.CarSpaces == 0 ? (object)DBNull.Value : property.CarSpaces,
                            property.FloorArea == 0 ? (object)DBNull.Value : property.FloorArea,
                            property.LandArea == 0 ? (object)DBNull.Value : property.LandArea,
                            property.Description ?? (object)DBNull.Value,
                            property.OccupancyType ?? (object)DBNull.Value,
                            property.OccupancyStatus ?? (object)DBNull.Value,
                            property.PropertyRelationshipId == 0 ? (object)DBNull.Value : property.PropertyRelationshipId,
                            property.LotNumber ?? (object)DBNull.Value,
                            property.AdministrativeArea ?? (object)DBNull.Value,
                            property.Locale ?? (object)DBNull.Value,
                            property.PostOfficeName ?? (object)DBNull.Value,
                            property.RuralDelivery ?? (object)DBNull.Value,
                            property.IsActive,
                            userDataSourceId,
                            property.BuildingNumber,
                            property.Unit,
                            property.StreetAddress
                        );
                    }

                    // Use stored procedure with table-valued parameter for MERGE operation
                    var parameters = new DynamicParameters();
                    parameters.Add("@PropertiesData", dataTable.AsTableValuedParameter("UserPropertiesType"));

                    string mergeQuery = $@"
                        MERGE {Constants.UserPropertyTableName} AS Target
                        USING @PropertiesData AS Source
                        ON Target.SRCEntitytId = Source.SRCEntitytId AND Target.UserId = Source.UserId AND (Target.SRCTenancyId = Source.SRCTenancyId OR Target.SRCManagementId = Source.SRCManagementId)
                        WHEN MATCHED THEN
                            UPDATE SET
                                SRCAgencyId = Source.SRCAgencyId,
                                SRCManagementId = Source.SRCManagementId,
                                SRCTenancyId = Source.SRCTenancyId,
                                PropertyName = Source.PropertyName,
                                StreetAddress = Source.StreetAddress,
                                Suburb = Source.Suburb,
                                City = Source.City,
                                StateID = Source.StateID,
                                CountryCode = Source.CountryCode,
                                PostalCode = Source.PostalCode,
                                PropertyType = Source.PropertyType,
                                Bedrooms = Source.Bedrooms,
                                BathRooms = Source.BathRooms,
                                CarSpaces = Source.CarSpaces,
                                FloorArea = Source.FloorArea,
                                LandArea = Source.LandArea,
                                Description = Source.Description,
                                OccupancyType = Source.OccupancyType,
                                OccupancyStatus = Source.OccupancyStatus,
                                PropertyRelationshipId = Source.PropertyRelationshipId,
                                LotNumber = Source.LotNumber,
                                AdministrativeArea = Source.AdministrativeArea,
                                Locale = Source.Locale,
                                PostOfficeName = Source.PostOfficeName,
                                RuralDelivery = Source.RuralDelivery,
                                IsActive = Source.IsActive,
                                UserDataSourceId = Source.UserDataSourceId,
                                ModifiedDate = GETUTCDATE(),
                                BuildingNumber = Source.BuildingNumber,
                                Unit = Source.Unit,
                                StreetNumber = Source.StreetNumber
                        WHEN NOT MATCHED THEN
                            INSERT (UserId, SRCEntitytId, SRCAgencyId, SRCManagementId, SRCTenancyId , PropertyName, 
                                   StreetAddress, Suburb, City, StateID, CountryCode, PostalCode, PropertyType,
                                   Bedrooms, BathRooms, CarSpaces, FloorArea, LandArea, Description, OccupancyType,
                                   OccupancyStatus, PropertyRelationshipId, LotNumber, AdministrativeArea, Locale,
                                   PostOfficeName, RuralDelivery, IsActive, UserDataSourceId, BuildingNumber, Unit, StreetNumber)
                            VALUES (Source.UserId, Source.SRCEntitytId, Source.SRCAgencyId, Source.SRCManagementId,Source.SRCTenancyId, 
                                   Source.PropertyName, Source.StreetAddress, Source.Suburb, 
                                   Source.City, Source.StateID, Source.CountryCode, Source.PostalCode, 
                                   Source.PropertyType, Source.Bedrooms, Source.BathRooms, Source.CarSpaces, 
                                   Source.FloorArea, Source.LandArea, Source.Description, Source.OccupancyType, 
                                   Source.OccupancyStatus, Source.PropertyRelationshipId, Source.LotNumber, 
                                   Source.AdministrativeArea, Source.Locale, Source.PostOfficeName, Source.RuralDelivery, 
                                   Source.IsActive, Source.UserDataSourceId, Source.BuildingNumber, Source.Unit, Source.StreetNumber)
                            OUTPUT 
                                $action AS MergeAction,
                                inserted.SRCTenancyId AS SRCId,  
                                inserted.SRCManagementId AS SRCManagementId,  
                                inserted.SRCEntitytId AS SRCPropertyId,
                                inserted.PropertyId AS PropertyId,
                                inserted.UserId;";

                    var results = (await QueryAsync<SQLQueryMergeResult>(mergeQuery, parameters, transaction, connection));
                    if(results != null && results.Any())
                    {
                        await BulkUpsertArchivePropertyAgency(dataTable, results, connection, transaction);
                    }
                    
                    _logger.LogInformation("Bulk upserted {RowsAffected} properties for {PropertyCount} properties with DataSourceId {DataSourceId}",
                        results.Count(), userPropertiesList.Count, dataSourceId);

                    return results.ToList();
                }
                catch (Exception ex)
                {
                    await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = (int)Constants.APIDetail.GetProperties,
                        InviteCode = null!,
                        UserId = null!,
                        IsCompleted = false,
                        ErrorInfo = "Sql merge statement error:  " + ex.Message,
                        IsDataSync = true,
                        AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(userPropertiesList)
                    });
                    _logger.LogError(ex, "Error in bulk upsert for {PropertyCount} properties", userPropertiesList.Count);
                    throw;
                }
            });
        }
        
        /// <summary>
        /// Get maintenance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of maintenance details</returns>
        public async Task<List<ViewMaintenanceDetail>> GetMaintenanceDetails(int propertyId, string? managementId, string? tenancyId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT MD.MaintenanceDetailId,MD.PropertyId, MD.SRCPropertyId,MD.SRCJobId, MD.JobSummary, MD.JobStatus, MD.SRCRequestId, MD.SRCPropertyId, MD.SRCJobId, MD.SRCManagementId,");
                query.Append($"MD.SRCTenancyId, MD.RequestSummary, MD.RequestStatus, MD.RequestRaisedBy,MD.RequestRaisedDate, MD.ImageLink, UP.SRCAgencyId AS AgencyId, AD.BusinessName AS AgencyName ");
                query.Append($"FROM {Constants.MaintenanceDetailsTableName} MD ");
                query.Append($"INNER JOIN {Constants.UserPropertyTableName} UP ON MD.PropertyId = UP.PropertyId ");
                query.Append($"LEFT JOIN {Constants.AgencyDetailsTableName} AD ON UP.SRCAgencyId = AD.AgencyId ");
                query.Append($"WHERE MD.PropertyId = @PropertyId ");
                if (!string.IsNullOrWhiteSpace(managementId))
                    query.Append($"AND MD.SRCManagementId = @ManagementId ");
                else if (!string.IsNullOrWhiteSpace(tenancyId))
                    query.Append($"AND MD.SRCTenancyId = @TenancyId ");
                query.Append($"ORDER BY MD.RequestRaisedDate DESC");

                var parameters = new { PropertyId = propertyId, ManagementId = managementId, TenancyId = tenancyId };
                var results = await GetAllAsync<ViewMaintenanceDetail>(query.ToString(), parameters);

                return results ?? new List<ViewMaintenanceDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting maintenance details for managementId {ManagementId} and propertyId {PropertyId}", managementId, propertyId);
                return new List<ViewMaintenanceDetail>();
            }
        }

        /// <summary>
        /// Get maintenance details with agency information based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of maintenance details with agency information</returns>
        public async Task<List<ViewMaintenanceDetail>> GetMaintenanceDetailsWithAgency(int propertyId, string? managementId, string? tenancyId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT MD.MaintenanceDetailId,MD.PropertyId, MD.SRCPropertyId, MD.SRCJobId, MD.JobSummary, MD.JobStatus, MD.SRCRequestId, MD.SRCManagementId,");
                query.Append($"MD.SRCTenancyId, MD.RequestSummary, MD.RequestStatus, MD.RequestRaisedBy,MD.RequestRaisedDate, MD.ImageLink, UP.SRCAgencyId AS AgencyId, AD.AgencyName ");
                query.Append($"FROM {Constants.MaintenanceDetailsTableName} MD ");
                query.Append($"INNER JOIN {Constants.UserPropertyTableName} UP ON MD.PropertyId = UP.PropertyId ");
                query.Append($"LEFT JOIN {Constants.AgencyDetailsTableName} AD ON UP.SRCAgencyId = AD.SRCAgencyId ");
                query.Append($"WHERE MD.PropertyId = @PropertyId ");
                if(!string.IsNullOrWhiteSpace(managementId))
                    query.Append($"AND MD.SRCManagementId = @ManagementId ");
                else if(!string.IsNullOrWhiteSpace(tenancyId))
                    query.Append($"AND MD.SRCTenancyId = @TenancyId ");
                query.Append($"ORDER BY MD.RequestRaisedDate DESC");

                var parameters = new { PropertyId = propertyId, ManagementId = managementId, TenancyId= tenancyId };
                var results = await GetAllAsync<ViewMaintenanceDetail>(query.ToString(), parameters);

                return results ?? new List<ViewMaintenanceDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting maintenance details with agency for managementId {ManagementId} and propertyId {PropertyId}", managementId, propertyId);
                return new List<ViewMaintenanceDetail>();
            }
        }

        /// <summary>
        /// Get compliance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of compliance details</returns>
        public async Task<List<ComplianceDetail>> GetCompliance(string managementId, int propertyId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT CD.ComplianceDetailId, CD.SRCManagementId, CD.SRCPropertyId, CD.PropertyId, CD.SRCComplianceId, ");
                query.Append($"CD.ComplianceName, CD.Status, CD.ExpiryDate, CD.ServicedBy ");
                query.Append($"FROM {Constants.ComplianceDetailsTableName} CD ");
                query.Append($"WHERE CD.PropertyId = @PropertyId ");
                query.Append($"AND CD.SRCManagementId = @ManagementId ");
                query.Append($"ORDER BY CD.ExpiryDate DESC");

                var parameters = new { PropertyId = propertyId, ManagementId = managementId };
                var results = await GetAllAsync<ComplianceDetail>(query.ToString(), parameters);

                return results ?? new List<ComplianceDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance details for managementId {ManagementId} and propertyId {PropertyId}", managementId, propertyId);
                return new List<ComplianceDetail>();
            }
        }

        /// <summary>
        /// Get inspections list based on tenancyId and propertyId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of inspection details</returns>
        public async Task<List<InspectionDetail>> GetInspections(int propertyId, string? managementId, string? tenancyId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT ID.InspectionsDetailId, ID.SRCTenancyId, ID.SRCManagementId, ID.SRCPropertyId, ID.PropertyId, ID.SRCInspectionId, ");
                query.Append($"ID.InspectionStatus, ID.InspectionDate, ID.InspectionStartTime, ID.InspectionEndTime,  ID.Summary ");
                query.Append($"FROM {Constants.InspectionDetailsTableName} ID ");
                query.Append($"WHERE ID.PropertyId = @PropertyId ");
                if (!string.IsNullOrWhiteSpace(tenancyId))
                    query.Append($"AND ID.SRCTenancyId = @TenancyId ");
                else if (!string.IsNullOrWhiteSpace(managementId))
                    query.Append($"AND ID.SRCManagementId = @ManagementId ");
                query.Append($"ORDER BY ID.InspectionDate DESC");

                var parameters = new { PropertyId = propertyId, ManagementId = managementId, TenancyId = tenancyId };
                var results = await GetAllAsync<InspectionDetail>(query.ToString(), parameters);

                return results ?? new List<InspectionDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inspections for tenancyId {TenancyId} and propertyId {PropertyId}", tenancyId, propertyId);
                return new List<InspectionDetail>();
            }
        }
        public async Task<List<UserPropertyDocumentDetail>> GetDocument(GetDocumentRequestModel requestModel, int userId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT UP.PropertyId, UP.UserId, UP.StreetAddress, UP.City, UP.CountryName, UP.StateName, UP.PropertyType, UP.PropertyRelationshipId, UP.PropertyName, ");
                query.Append($"DD.SRCManagementId, DD.SRCTenancyId, DD.DocumentName, DD. DocumentLink, DD.DocumentType, DD.MetaType, DD.MetaNumber, DD.MetaDate, ");
                query.Append($"DD.MetaStatus, DD.MetaAmount, DD.MetaOwing, DD.MetaCurrency, DD.MetaPeriod, DD.SharedDate, DD.LastUpdatedDate, UN.PropertyNickName ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.DocumentDetailsTableName} DD ");
                query.Append($"ON (DD.SRCManagementId = UP.SRCManagementId OR DD.SRCTenancyId = UP.SRCTenancyId) ");
                query.Append($"LEFT JOIN {Constants.UserPropertiesNickNameTableName} UN ON UN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId ");
                query.Append($"WHERE UP.UserId = @UserId ");
                if (!string.IsNullOrEmpty(requestModel.TenancyId))
                    query.Append($"AND DD.SRCTenancyId = @TenancyId ");
                else if (!string.IsNullOrEmpty(requestModel.ManagementId))
                    query.Append($"AND DD.SRCManagementId = @ManagementId ");

                query.Append($"ORDER BY DD.SharedDate DESC ");

                // Only apply pagination if showAllRecords is false
                if (requestModel.ShowAllRecords != true)
                {
                    query.Append($"OFFSET @OffSet ROWS FETCH NEXT @Limit ROWS ONLY");
                }

                var parameters = new { ManagementId = requestModel.ManagementId, TenancyId = requestModel.TenancyId, UserId = userId, OffSet = requestModel.OffSet, Limit = requestModel.Limit };
                var results = await GetAllAsync<UserPropertyDocumentDetail>(query.ToString(), parameters);

                return results ?? new List<UserPropertyDocumentDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting documents for tenancyId {TenancyId} and ManagementId {ManagementId}", requestModel.TenancyId, requestModel.ManagementId);
                return new List<UserPropertyDocumentDetail>();
            }
        }

        /// <summary>
        /// Get property manager information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property manager information with agency details</returns>
        public async Task<PropertyManagerWithAgencyDetails?> GetPropertyManagerInformation(string? managementId, int? propertyId, string? srcPropertyId)
        {
            try
            {
                var query = new StringBuilder();
                query.Append($"SELECT TOP 1 PM.PropertyManagerInformationId, PM.PropertyId, PM.SRCAgencyId, PM.SRCPropertyId, PM.SRCManagementId, ");
                query.Append($"PM.ManagementType, AD.BusinessName AS AgencyName, PM.PropertyManagerName, PM.PropertyManagerMobile, ");
                query.Append($"PM.PropertyManagerPhone, PM.PropertyManagerEmail, PM.AuthorityStartDate, PM.AuthorityEndDate, ");
                query.Append($"PM.Ownership, PM.ExpenditureLimit, PM.ExpenditureNotes, PM.ContactRole, ");
                query.Append($"AD.BusinessRegisteredName, AD.BusinessName ");
                query.Append($"FROM {Constants.PropertyManagerInformationTableName} PM ");
                query.Append($"LEFT JOIN {Constants.AgencyDetailsTableName} AD ON PM.SRCAgencyId = AD.AgencyId ");
                
                // Dynamic WHERE clause based on provided parameters
                var conditions = new List<string>();
                var parameters = new DynamicParameters();

                if (!string.IsNullOrWhiteSpace(managementId))
                {
                    conditions.Add("PM.SRCManagementId = @ManagementId");
                    parameters.Add("ManagementId", managementId);
                }

                if (propertyId.HasValue && propertyId.Value > 0)
                {
                    conditions.Add("PM.PropertyId = @PropertyId");
                    parameters.Add("PropertyId", propertyId.Value);
                }

                if (!string.IsNullOrWhiteSpace(srcPropertyId))
                {
                    conditions.Add("PM.SRCPropertyId = @SRCPropertyId");
                    parameters.Add("SRCPropertyId", srcPropertyId);
                }

                if (conditions.Count > 0)
                {
                    // Use AND logic when multiple parameters are provided to find unique record
                    query.Append($"WHERE {string.Join(" AND ", conditions)}");
                }

                return await GetByIdAsync<PropertyManagerWithAgencyDetails>(query.ToString(), parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property manager information for ManagementId: {ManagementId}, PropertyId: {PropertyId}, SRCPropertyId: {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                return null;
            }
        }

        /// <summary>
        /// Get property financial information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property financial information with agency details</returns>
        public async Task<PropertyFinancialWithAgencyDetails?> GetPropertyFinancialInformation(string? managementId, int? propertyId, string? srcPropertyId)
        {
            try
            {
                var query = new StringBuilder();
                query.Append($"SELECT TOP 1 PF.PropertyFinancialInformationId, PF.PropertyId, PF.TenancyName, PF.LeaseStart, PF.LeaseEnd, ");
                query.Append($"PF.VacateDate, PF.Rent, PF.IncreaseRent, PF.IncreaseDate, PF.OptionsDate, PF.OptionsDetail, ");
                query.Append($"PF.Arrears, PF.PayToDate, PF.AmountToVacate, PF.OutstandingInvoices, PF.InvoiceFeesArrears, ");
                query.Append($"PF.RentCharge, PF.WeeklyRent, PF.LastPaid, PF.HeldFunds, ");
                query.Append($"PF.OwnershipTotalAvailableBalance, PF.PropertyOutstandingFees, PF.PropertyOutstandingInvoices, ");
                query.Append($"PF.PropertyOverdueInvoices, PF.LastPaymentAmount, PF.Currency, CD.Symbol AS CurrencySymbol, PF.LastStatementDate, ");
                query.Append($"UP.SRCAgencyId, UP.SRCManagementId, UP.SRCEntitytId AS SRCPropertyId, ");
                query.Append($"AD.BusinessName AS AgencyName, AD.BusinessRegisteredName, AD.BusinessName ");
                query.Append($"FROM {Constants.PropertyFinancialInformationTableName} PF ");
                query.Append($"LEFT JOIN {Constants.UserPropertyTableName} UP ON PF.PropertyId = UP.PropertyId ");
                query.Append($"LEFT JOIN {Constants.AgencyDetailsTableName} AD ON UP.SRCAgencyId = AD.AgencyId ");
                query.Append($"LEFT JOIN {Constants.CurrencyDetailTableName} CD ON PF.Currency = CD.CurrencyCode ");

                // Dynamic WHERE clause based on provided parameters
                var conditions = new List<string>();
                var parameters = new DynamicParameters();

                if (!string.IsNullOrWhiteSpace(managementId))
                {
                    conditions.Add("UP.SRCManagementId = @ManagementId");
                    parameters.Add("ManagementId", managementId);
                }

                if (propertyId.HasValue && propertyId.Value > 0)
                {
                    conditions.Add("PF.PropertyId = @PropertyId");
                    parameters.Add("PropertyId", propertyId.Value);
                }

                if (!string.IsNullOrWhiteSpace(srcPropertyId))
                {
                    conditions.Add("UP.SRCEntitytId = @SRCPropertyId");
                    parameters.Add("SRCPropertyId", srcPropertyId);
                }

                if (conditions.Count > 0)
                {
                    // Use AND logic when multiple parameters are provided to find unique record
                    query.Append($"WHERE {string.Join(" AND ", conditions)}");
                }

                return await GetByIdAsync<PropertyFinancialWithAgencyDetails>(query.ToString(), parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property financial information for ManagementId: {ManagementId}, PropertyId: {PropertyId}, SRCPropertyId: {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                return null;
            }
        }

        public async Task<TenanciesTenantDetailResponse> GetTenantOwnerDetail(string? tenancyId, string? srcPropertyId, int? propertyId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT TOP 1 UP.PropertyId, UP.SRCEntitytId AS SRCPropertyId, UP.SRCAgencyId, UP.SRCManagementId, UP.SRCTenancyId, ");
                query.Append($"PF.TenancyName, PF.LeaseStart, PF.LeaseEnd, PF.VacateDate, PF.Rent, PF.IncreaseRent, PF.IncreaseDate, ");
                query.Append($"PF.AmountToVacate, PF.PayToDate, PF.RentPeriod, PF.Currency, CD.Symbol AS CurrencySymbol, PF.Arrears, PF.OutstandingInvoices, ");
                query.Append($"PM.PropertyManagerName, PM.PropertyManagerMobile, PM.PropertyManagerEmail, PM.AuthorityStartDate, PM.AuthorityEndDate, PM.ContactRole, ");
                query.Append($"AD.MriId, AD.BusinessName AS AgencyName,AD.BusinessName, AD.BusinessRegisteredName, AD.BusinessRegistrationNumber, AD.Phone AS AgencyPhone, AD.Email AS AgencyEmail ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.PropertyFinancialInformationTableName} PF ON UP.PropertyId = PF.PropertyId ");
                query.Append($"LEFT JOIN {Constants.PropertyManagerInformationTableName} PM ON PF.PropertyId = PM.PropertyId ");
                query.Append($"LEFT JOIN {Constants.AgencyDetailsTableName} AD ON UP.SRCAgencyId = AD.AgencyId ");
                query.Append($"LEFT JOIN {Constants.CurrencyDetailTableName} CD ON PF.Currency = CD.CurrencyCode ");
                query.Append($"WHERE UP.IsActive = 1 ");
                
                if(!string.IsNullOrWhiteSpace(tenancyId))
                    query.Append($"AND UP.SRCTenancyId = @TenancyId ");
                else if(!string.IsNullOrWhiteSpace(srcPropertyId))
                    query.Append($"AND UP.SRCEntitytId = @SRCPropertyId ");
                else if(propertyId > 0)
                    query.Append($"AND UP.PropertyId = @PropertyId");

                var parameters = new { TenancyId = tenancyId, SRCPropertyId = srcPropertyId, PropertyId = propertyId };

                return await GetByIdAsync<TenanciesTenantDetailResponse>(query.ToString(), parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Tenant Owner data for TenancyId { TenancyId}, SRCPropertyId { SRCPropertyId}, PropertyId { PropertyId}", tenancyId, srcPropertyId, propertyId);
                return null;
            }
        }
        private async Task<List<AgencyDetailResponse>> GetAgencyDetails(string[] agencyIds)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT AD.[AgencyDetailsId], AD.[AgencyId], AD.[MriId], AD.[BusinessRegisteredName], AD.[BusinessName], AD.[BusinessRegistrationNumber], AD.[Phone], AD.[Email], AD.[DarkLogoLink], ");
                query.Append($"AD.[LightLogoLink], AD.[BrandingBackgroundColor], AD.[CountryCode], AD.[CountryName], AD.[StateCode], AD.[StateName], AD.[Suburb], AD.[PostalCode], AD.[AdministrativeArea], ");
                query.Append($"AD.[BuildingNumber], AD.[LotNumber], AD.[StreetAddress], AD.[City], AD.[Locale], AD.[RuralDelivery], AD.[PostOfficeName], AD.[DataSourceId] ");
                query.Append($"FROM {Constants.AgencyDetailsTableName} AD ");
                query.Append($"WHERE AD.AgencyId IN @AgencyIds ");
                query.Append($"ORDER BY AD.CreatedDate DESC ");

                var parameters = new { AgencyIds = agencyIds};
                return await GetAllAsync<AgencyDetailResponse>(query.ToString(), parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Agency data for AgencyId {AgencyId}", agencyIds);
                return null;
            }
        }

        private async Task BulkUpsertArchivePropertyAgency(DataTable dataTable, IEnumerable<SQLQueryMergeResult> sqlResultList, IDbConnection connection, IDbTransaction transaction)
        {
            try
            {
                var archivePropertyDT = GetInactiveProperties(dataTable);
                if (archivePropertyDT == null || archivePropertyDT.Rows.Count == 0)
                    return;

                var agencyIds = GetDistinctAgencyIds(archivePropertyDT);
                var agencyList = await GetAgencyDetails(agencyIds);
                var archiveAgencyData = MapAgencyData(archivePropertyDT, sqlResultList, agencyList);

                if (!archiveAgencyData.Any())
                    return;

                var parameters = new DynamicParameters();
                parameters.Add("@ArchivePropertiesAgencyData", archiveAgencyData.ToDataTable().AsTableValuedParameter("ArchivePropertyAgencyDetailsType"));

                string mergeQuery = GetArchivePropertiesAgencyDetailMergeQuery();
                var results = await QueryAsync<SQLQueryMergeResult>(mergeQuery, parameters, transaction, connection);
            }
            catch (Exception ex)
            {
                var errorInfo = new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetProperties,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Error in BulkUpsertArchivePropertyAgency function sql merge statement error: " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(dataTable)
                };

                await _apiTrackingRepository.LogAPITrackingDetails(errorInfo);
                _logger.LogError(ex, "Error in bulk upsert in BulkUpsertArchivePropertyAgency function for {data}", System.Text.Json.JsonSerializer.Serialize(dataTable));
            }
        }

        private DataTable GetInactiveProperties(DataTable dataTable)
        {
            var inactiveRows = dataTable.AsEnumerable()
                .Where(row => !row.Field<bool>("IsActive"));

            return inactiveRows.Any() ? inactiveRows.CopyToDataTable() : null;
        }

        private string[] GetDistinctAgencyIds(DataTable dataTable)
        {
            return dataTable.AsEnumerable()
                .Select(row => row.Field<string>("SRCAgencyId")!)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToArray();
        }

        private List<AgencyDetailResponse> MapAgencyData(DataTable dataTable, IEnumerable<SQLQueryMergeResult> sqlResultList, List<AgencyDetailResponse> agencyList)
        {
            var result = new List<AgencyDetailResponse>();

            foreach (DataRow row in dataTable.Rows)
            {
                string srcPropertyId = row["SRCEntitytId"].ToString();
                string srcManagementId = row["SRCManagementId"].ToString();
                string srcTenancyId = row["SRCTenancyId"]?.ToString();

                var agency = agencyList.FirstOrDefault(a => a.AgencyId == row["SRCAgencyId"].ToString());
                var match = sqlResultList.FirstOrDefault(u =>
                    u.SRCPropertyId == srcPropertyId &&
                    (u.SRCManagementId == srcManagementId || u.SRCId == srcTenancyId));

                if (match != null && agency != null)
                {
                    agency.PropertyId = match.PropertyId;
                    agency.SRCPropertyId = srcPropertyId;
                    result.Add(agency);
                }
            }

            return result;
        }
        private string GetArchivePropertiesAgencyDetailMergeQuery()
        {
            return $@"
                MERGE {Constants.ArchivePropertyAgencyDetailsTableName} AS Target
                USING @ArchivePropertiesAgencyData AS Source
                ON Target.PropertyId = Source.PropertyId AND Target.AgencyId = Source.AgencyId AND Target.AgencyDetailsId = Source.AgencyDetailsId
                WHEN MATCHED THEN
                    UPDATE SET
                        SRCPropertyId = Source.SRCPropertyId,
                        MriId = Source.MriId,
                        BusinessRegisteredName = Source.BusinessRegisteredName,
                        BusinessName = Source.BusinessName,
                        BusinessRegistrationNumber = Source.BusinessRegistrationNumber,
                        Phone = Source.Phone,
                        Email = Source.Email,
                        DarkLogoLink = Source.DarkLogoLink,
                        LightLogoLink = Source.LightLogoLink,
                        BrandingBackgroundColor = Source.BrandingBackgroundColor,
                        CountryCode = Source.CountryCode,
                        CountryName = Source.CountryName,
                        StateCode = Source.StateCode,
                        StateName = Source.StateName,
                        Suburb = Source.Suburb,
                        PostalCode = Source.PostalCode,
                        AdministrativeArea = Source.AdministrativeArea,
                        BuildingNumber = Source.BuildingNumber,
                        LotNumber = Source.LotNumber,
                        StreetAddress = Source.StreetAddress,
                        City = Source.City,
                        Locale = Source.Locale,
                        RuralDelivery = Source.RuralDelivery,
                        PostOfficeName = Source.PostOfficeName,
                        ModifiedDate = GETUTCDATE()
                WHEN NOT MATCHED THEN
                    INSERT (PropertyId, SRCPropertyId, AgencyDetailsId, AgencyId, MriId, DataSourceId, BusinessRegisteredName, BusinessName,
                            BusinessRegistrationNumber, Phone, Email, DarkLogoLink, LightLogoLink,
                            BrandingBackgroundColor, CountryCode, CountryName, StateCode, StateName,
                            Suburb, PostalCode, AdministrativeArea, BuildingNumber, LotNumber,
                            StreetAddress, City, Locale, RuralDelivery, PostOfficeName)
                    VALUES (Source.PropertyId, Source.SRCPropertyId, Source.AgencyDetailsId, Source.AgencyId, Source.MriId, Source.DataSourceId, Source.BusinessRegisteredName,
                            Source.BusinessName, Source.BusinessRegistrationNumber, Source.Phone, Source.Email,
                            Source.DarkLogoLink, Source.LightLogoLink, Source.BrandingBackgroundColor,
                            Source.CountryCode, Source.CountryName, Source.StateCode, Source.StateName,
                            Source.Suburb, Source.PostalCode, Source.AdministrativeArea, Source.BuildingNumber,
                            Source.LotNumber, Source.StreetAddress, Source.City, Source.Locale,
                            Source.RuralDelivery, Source.PostOfficeName)
                    OUTPUT 
                        $action AS MergeAction,
                        inserted.ArchivePropertyAgencyDetailId AS SRCId,
                        null AS SRCManagementId,
                        inserted.SRCPropertyId AS SRCPropertyId,
                        inserted.PropertyId AS PropertyId,
                        null AS UserId;";
        }
        public async Task<List<UserProperties>> CheckExsitingProperties(int UserId, string[] srcAgencyIds, string[] srcPropertyIds, int[] propertyRelationshipIds, string[] srcManagementIds, string[] srcTenancyIds)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT UP.* ");
            query.Append($"FROM {Constants.UserPropertyTableName} UP ");
            query.Append($"WHERE UP.UserId =  @UserId ");
            query.Append($"AND UP.SRCAgencyId IN @SRCAgencyIds ");
            query.Append($"AND UP.SRCEntitytId IN @SRCPropertyIds ");
            query.Append($"AND UP.PropertyRelationshipId IN @PropertyRelationshipIds ");

            if(srcManagementIds != null && srcManagementIds.Any())
                query.Append($"AND UP.SRCManagementId IN @SRCManagementIds ");
            else if (srcTenancyIds != null && srcTenancyIds.Any())
                query.Append($"AND UP.SRCTenancyId IN @SRCTenancyIds ");

            var parameters = new { UserId = UserId, SRCAgencyIds = srcAgencyIds, SRCPropertyIds = srcPropertyIds, PropertyRelationshipIds = propertyRelationshipIds, SRCManagementIds = srcManagementIds, SRCTenancyIds = srcTenancyIds };
            return await GetAllAsync<UserProperties>(query.ToString(), parameters);
        }
    }
}

