using Azure.Data.AppConfiguration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.FeatureFlags;

namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// Service for managing feature flags from Azure App Configuration
    /// </summary>
    public class FeatureFlagService : IFeatureFlagService
    {
        private readonly IFeatureManager _featureManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<FeatureFlagService> _logger;

        /// <summary>
        /// Constructor for FeatureFlagService
        /// </summary>
        /// <param name="featureManager">Feature manager instance</param>
        /// <param name="configuration">Configuration instance</param>
        /// <param name="logger">Logger instance</param>
        public FeatureFlagService(
            IFeatureManager featureManager,
            IConfiguration configuration,
            ILogger<FeatureFlagService> logger)
        {
            _featureManager = featureManager;
            _configuration = configuration;
            _logger = logger;
        }







        /// <summary>
        /// Get all configured feature flags from Azure App Configuration for a specific user
        /// </summary>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>List of feature flag response models</returns>
        public async Task<List<FeatureFlagResponseModel>> GetAllFeatureFlagsForUserAsync(string userIdentifier)
        {
            try
            {
                var featureFlags = new List<FeatureFlagResponseModel>();
                var connectionString = _configuration["FeatureManagement:ConnectionString"];

                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogWarning("Azure App Configuration connection string is not configured");
                    return featureFlags;
                }

                // Create Azure App Configuration client
                var client = new ConfigurationClient(connectionString);

                // Get all feature flags from Azure App Configuration
                var featureFlagSelector = new SettingSelector
                {
                    KeyFilter = ".appconfig.featureflag/*"
                };

                await foreach (var setting in client.GetConfigurationSettingsAsync(featureFlagSelector))
                {
                    try
                    {
                        // Extract feature flag name from the key
                        var featureName = setting.Key.Replace(".appconfig.featureflag/", "");

                        // Check if the feature is enabled for the user
                        var isEnabled = await _featureManager.IsEnabledAsync(featureName);

                        featureFlags.Add(new FeatureFlagResponseModel
                        {
                            FeatureName = featureName,
                            IsEnabled = isEnabled,
                            UserEmail = userIdentifier,
                            CheckedAt = DateTime.UtcNow,
                            Source = "Azure App Configuration",
                            Description = GetFeatureDescription(featureName)
                        });

                        _logger.LogDebug("Retrieved feature flag {FeatureName} with status {IsEnabled} for user {UserIdentifier}",
                            featureName, isEnabled, userIdentifier);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing feature flag setting {Key}", setting.Key);
                    }
                }

                _logger.LogInformation("Retrieved {Count} feature flags from Azure App Configuration for user {UserIdentifier}",
                    featureFlags.Count, userIdentifier);

                return featureFlags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feature flags from Azure App Configuration for user {UserIdentifier}", userIdentifier);
                return new List<FeatureFlagResponseModel>();
            }
        }



        /// <summary>
        /// Get description for a feature flag (can be extended to read from configuration or database)
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>Description of the feature</returns>
        private string? GetFeatureDescription(string featureName)
        {
            // This could be extended to read descriptions from configuration or a database
            return featureName switch
            {
                "EnableNewFeature" => "Enables the new feature functionality",
                "EnableBetaFeatures" => "Enables beta features for testing",
                "EnableAdvancedLogging" => "Enables advanced logging capabilities",
                _ => $"Feature flag: {featureName}"
            };
        }
    }
}
