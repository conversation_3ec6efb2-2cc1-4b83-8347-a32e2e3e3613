using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.FeatureFlags;

namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// Service for managing feature flags
    /// </summary>
    public class FeatureFlagService : IFeatureFlagService
    {
        private readonly IFeatureManager _featureManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<FeatureFlagService> _logger;

        /// <summary>
        /// Constructor for FeatureFlagService
        /// </summary>
        /// <param name="featureManager">Feature manager instance</param>
        /// <param name="configuration">Configuration instance</param>
        /// <param name="logger">Logger instance</param>
        public FeatureFlagService(
            IFeatureManager featureManager,
            IConfiguration configuration,
            ILogger<FeatureFlagService> logger)
        {
            _featureManager = featureManager;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Check if a feature is enabled
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>True if feature is enabled, false otherwise</returns>
        public async Task<bool> IsFeatureEnabledAsync(string featureName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(featureName))
                {
                    _logger.LogWarning("Feature name is null or empty");
                    return false;
                }

                var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                _logger.LogDebug("Feature {FeatureName} is {Status}", featureName, isEnabled ? "enabled" : "disabled");
                return isEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feature flag {FeatureName}", featureName);
                return false;
            }
        }

        /// <summary>
        /// Check if a feature is enabled for a specific user
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>True if feature is enabled for the user, false otherwise</returns>
        public async Task<bool> IsFeatureEnabledForUserAsync(string featureName, string userIdentifier)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(featureName))
                {
                    _logger.LogWarning("Feature name is null or empty");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(userIdentifier))
                {
                    _logger.LogWarning("User identifier is null or empty, falling back to general feature check");
                    return await IsFeatureEnabledAsync(featureName);
                }

                // Create context for user-specific evaluation
                var context = new FeatureFlagContext
                {
                    UserId = userIdentifier,
                    Properties = new Dictionary<string, object> { { "UserIdentifier", userIdentifier } }
                };

                // For now, we'll use the basic feature manager
                // In the future, this could be extended to support user-specific targeting based on email domains, etc.
                var isEnabled = await _featureManager.IsEnabledAsync(featureName);

                _logger.LogDebug("Feature {FeatureName} is {Status} for user {UserIdentifier}",
                    featureName, isEnabled ? "enabled" : "disabled", userIdentifier);

                return isEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feature flag {FeatureName} for user {UserIdentifier}", featureName, userIdentifier);
                return false;
            }
        }

        /// <summary>
        /// Get all configured feature flags with their current status
        /// </summary>
        /// <returns>List of feature flag information</returns>
        public async Task<List<FeatureFlagInfo>> GetAllFeatureFlagsAsync()
        {
            try
            {
                var featureFlags = new List<FeatureFlagInfo>();
                var featureManagementSection = _configuration.GetSection("FeatureManagement");
                var featureNames = featureManagementSection.GetChildren().Select(x => x.Key).ToList();

                foreach (var featureName in featureNames)
                {
                    var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                    featureFlags.Add(new FeatureFlagInfo
                    {
                        Name = featureName,
                        IsEnabled = isEnabled,
                        CheckedAt = DateTime.UtcNow,
                        Source = "Configuration",
                        Description = GetFeatureDescription(featureName)
                    });
                }

                _logger.LogInformation("Retrieved {Count} feature flags", featureFlags.Count);
                return featureFlags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all feature flags");
                return new List<FeatureFlagInfo>();
            }
        }

        /// <summary>
        /// Get all configured feature flags with their current status for a specific user
        /// </summary>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>List of feature flag response models</returns>
        public async Task<List<FeatureFlagResponseModel>> GetAllFeatureFlagsForUserAsync(string userIdentifier)
        {
            try
            {
                var featureFlags = new List<FeatureFlagResponseModel>();
                var featureManagementSection = _configuration.GetSection("FeatureManagement");
                var featureNames = featureManagementSection.GetChildren().Select(x => x.Key).ToList();

                foreach (var featureName in featureNames)
                {
                    var isEnabled = await IsFeatureEnabledForUserAsync(featureName, userIdentifier);
                    featureFlags.Add(new FeatureFlagResponseModel
                    {
                        FeatureName = featureName,
                        IsEnabled = isEnabled,
                        UserEmail = userIdentifier,
                        CheckedAt = DateTime.UtcNow,
                        Source = "Configuration",
                        Description = GetFeatureDescription(featureName)
                    });
                }

                _logger.LogInformation("Retrieved {Count} feature flags for user {UserIdentifier}", featureFlags.Count, userIdentifier);
                return featureFlags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all feature flags for user {UserIdentifier}", userIdentifier);
                return new List<FeatureFlagResponseModel>();
            }
        }

        /// <summary>
        /// Get feature flag information by name
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>Feature flag information</returns>
        public async Task<FeatureFlagInfo?> GetFeatureFlagAsync(string featureName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(featureName))
                {
                    _logger.LogWarning("Feature name is null or empty");
                    return null;
                }

                var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                
                return new FeatureFlagInfo
                {
                    Name = featureName,
                    IsEnabled = isEnabled,
                    CheckedAt = DateTime.UtcNow,
                    Source = "Configuration",
                    Description = GetFeatureDescription(featureName)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feature flag {FeatureName}", featureName);
                return null;
            }
        }

        /// <summary>
        /// Check multiple feature flags at once
        /// </summary>
        /// <param name="featureNames">List of feature names to check</param>
        /// <returns>Dictionary with feature names and their status</returns>
        public async Task<Dictionary<string, bool>> CheckMultipleFeaturesAsync(IEnumerable<string> featureNames)
        {
            var results = new Dictionary<string, bool>();

            try
            {
                var validFeatureNames = featureNames?.Where(f => !string.IsNullOrWhiteSpace(f)).ToList() ?? new List<string>();
                
                if (!validFeatureNames.Any())
                {
                    _logger.LogWarning("No valid feature names provided");
                    return results;
                }

                foreach (var featureName in validFeatureNames)
                {
                    var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                    results[featureName] = isEnabled;
                }

                _logger.LogDebug("Checked {Count} feature flags", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking multiple feature flags");
                return results;
            }
        }

        /// <summary>
        /// Check multiple feature flags for a specific user
        /// </summary>
        /// <param name="featureNames">List of feature names to check</param>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>Dictionary with feature names and their status for the user</returns>
        public async Task<Dictionary<string, bool>> CheckMultipleFeaturesForUserAsync(IEnumerable<string> featureNames, string userIdentifier)
        {
            var results = new Dictionary<string, bool>();

            try
            {
                var validFeatureNames = featureNames?.Where(f => !string.IsNullOrWhiteSpace(f)).ToList() ?? new List<string>();

                if (!validFeatureNames.Any())
                {
                    _logger.LogWarning("No valid feature names provided");
                    return results;
                }

                foreach (var featureName in validFeatureNames)
                {
                    var isEnabled = await IsFeatureEnabledForUserAsync(featureName, userIdentifier);
                    results[featureName] = isEnabled;
                }

                _logger.LogDebug("Checked {Count} feature flags for user {UserIdentifier}", results.Count, userIdentifier);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking multiple feature flags for user {UserIdentifier}", userIdentifier);
                return results;
            }
        }

        /// <summary>
        /// Get description for a feature flag (can be extended to read from configuration or database)
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>Description of the feature</returns>
        private string? GetFeatureDescription(string featureName)
        {
            // This could be extended to read descriptions from configuration or a database
            return featureName switch
            {
                "EnableNewFeature" => "Enables the new feature functionality",
                "EnableBetaFeatures" => "Enables beta features for testing",
                "EnableAdvancedLogging" => "Enables advanced logging capabilities",
                _ => null
            };
        }
    }
}
