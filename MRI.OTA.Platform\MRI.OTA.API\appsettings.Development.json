{
    "ConnectionStrings": {
        "DefaultConnection": "Server=localhost; Database=OTA_Latest7; Integrated Security=True; TrustServerCertificate=true;Pooling=true; Max Pool Size=100; Min Pool Size=10;"
    },
  "ApplicationOption": {
    "BaseUrl": "https://ca-nw02-shrdplt-dev-web.purpleflower-483ee117.australiaeast.azurecontainerapps.io/",
    "DefaultImageUrl": "https://stgnw02shrdpltdev.blob.core.windows.net/azure-ota-images/PTAgencyDefaultLogo.png",
    "BatchSize": 50
  },
  "Authentication": {
    "TenantId": "218731ff-b7f8-457f-ae11-b1b13bfc738a",
    "TenantName": "sharedplatformotadev",
    "ClientId": "********-a4fa-4b2f-911c-3885a8dba92b",
    "B2CInstance": "https://sharedplatformotadev.b2clogin.com",
    "Domain": "sharedplatformotadev.onmicrosoft.com",
    "SignUpSignInPolicyId": "b2c_1_login_1",
    "Scopes": [ "openid", "access_api" ]
  },
  "AzureStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=stgnw02shrdpltdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ContainerName": "azure-ota-images"
  },
  "Cors": {
    "AllowedOrigins": "http://localhost:3001,http://localhost:8081,http://localhost:8080"
  },
  "SendGridSettings": {
    "ApiKey": "testkey",
    "DefaultFromEmail": "*******",
    "DefaultFromName": "Your Company"
  },
  "AzureCommunicationSettings": {
    "ConnectionString": "endpoint=https://comsvc-nw02-shrdplt-dev.australia.communication.azure.com/;accesskey=9AcwwVE8qRF5ty7TrZ4GkxifTe4i6W8oHiGe9Vy2hFQsmSNrH5UGJQQJ99BEACULyCpfTAzNAAAAAZCSjrcz",
    "DefaultFromEmail": "*******"
  },
  "RedisConfig": {
    "ConnectionString": "redis-nw02-shrdplt-dev.redis.cache.windows.net:6380,password=5xg3Ll9WQTmLAjyxabaQCxwARFTZVsNUWAzCaFNcGNU=,ssl=True,abortConnect=False",
    "DefaultExpirationMinutes": 60
  },
  "ImageUploadSettings": {
    "AllowedExtensions": [ ".jpg", ".jpeg", ".png", ".gif" ],
    "MaxFileSizeInBytes": 20 // In MB
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "System": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning"
    }
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.ApplicationInsights" ],
    "WriteTo": [
      {
        "Name": "ApplicationInsights",
        "Args": {
          "connectionString": "",
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId", "WithThreadName", "WithEventType" ],
    "Properties": {
      "Application": "MRI OTA APIs"
    }
  },
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  "FirebaseSettings": {
    "type": "service_account",
    "project_id": "mri-ota",
    "private_key_id": "b899ebb80a3c76912a91f6f0bc299609c8968d8a",
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "client_email": "*******",
    "client_id": "101221858729784109220",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40mri-ota.iam.gserviceaccount.com",
    "universe_domain": "googleapis.com"
  }
}
