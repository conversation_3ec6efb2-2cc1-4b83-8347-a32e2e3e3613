using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.FeatureFlags;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.FeatureFlags.v1
{
    /// <summary>
    /// Controller to handle feature flags operations
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/feature-flags")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class FeatureFlagsController : ControllerBase
    {
        private readonly IFeatureFlagService _featureFlagService;
        private readonly ILogger<FeatureFlagsController> _logger;
        private readonly TaskContext _taskContext;

        /// <summary>
        /// Constructor for FeatureFlagsController
        /// </summary>
        /// <param name="featureFlagService">Feature flag service</param>
        /// <param name="logger">Logger service</param>
        /// <param name="taskContext">Task context containing user information</param>
        public FeatureFlagsController(
            IFeatureFlagService featureFlagService,
            ILogger<FeatureFlagsController> logger,
            TaskContext taskContext)
        {
            _featureFlagService = featureFlagService;
            _logger = logger;
            _taskContext = taskContext;
        }



        /// <summary>
        /// Get the status of all configured feature flags for the current user
        /// </summary>
        /// <returns>List of all feature flags and their statuses for the current user</returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Get all feature flags", Description = "Retrieves the current status of all configured feature flags for the authenticated user based on their email address")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<List<FeatureFlagResponse>>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ApiResponse<List<FeatureFlagResponse>>>> GetAllFeatureFlags()
        {
            try
            {
                _logger.LogInformation("Getting all feature flags status for user email: {Email}", _taskContext.Email);

                var featureFlagInfos = await _featureFlagService.GetAllFeatureFlagsAsync();
                var userEmail = _taskContext.Email ?? string.Empty;

                var featureFlags = new List<FeatureFlagResponse>();

                foreach (var featureFlagInfo in featureFlagInfos)
                {
                    var isEnabledForUser = await _featureFlagService.IsFeatureEnabledForUserAsync(featureFlagInfo.Name, userEmail);
                    featureFlags.Add(new FeatureFlagResponse
                    {
                        FeatureName = featureFlagInfo.Name,
                        IsEnabled = isEnabledForUser,
                        UserEmail = userEmail,
                        CheckedAt = DateTime.UtcNow,
                        Description = featureFlagInfo.Description
                    });
                }

                _logger.LogInformation("Retrieved {Count} feature flags for user email {Email}", featureFlags.Count, _taskContext.Email);

                return Ok(new ApiResponse<List<FeatureFlagResponse>>(
                    true,
                    StatusCodes.Status200OK,
                    "Feature flags retrieved successfully",
                    featureFlags));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all feature flags for user email: {Email}", _taskContext.Email);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<object>(
                        false,
                        "An error occurred while retrieving feature flags",
                        data: null!,
                        StatusCodes.Status500InternalServerError,
                        new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Check multiple feature flags at once for the current user
        /// </summary>
        /// <param name="request">List of feature names to check</param>
        /// <returns>Status of requested feature flags for the current user</returns>
        [HttpPost("check")]
        [SwaggerOperation(Summary = "Check multiple feature flags", Description = "Checks the status of multiple feature flags for the authenticated user based on their email address in a single request")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<List<FeatureFlagResponse>>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ApiResponse<List<FeatureFlagResponse>>>> CheckFeatureFlags([FromBody] FeatureFlagCheckRequest request)
        {
            try
            {
                _logger.LogInformation("Checking multiple feature flags: {FeatureNames} for user email: {Email}",
                    string.Join(", ", request?.FeatureNames ?? new List<string>()), _taskContext.Email);

                if (request == null || request.FeatureNames == null || !request.FeatureNames.Any())
                {
                    return BadRequest(new ApiResponse<object>(
                        false,
                        "Feature names list cannot be empty",
                        data: null!,
                        StatusCodes.Status400BadRequest,
                        new List<string> { "At least one feature name is required" }));
                }

                var userEmail = _taskContext.Email ?? string.Empty;
                var featureFlags = new List<FeatureFlagResponse>();

                foreach (var featureName in request.FeatureNames.Where(f => !string.IsNullOrWhiteSpace(f)))
                {
                    var isEnabled = await _featureFlagService.IsFeatureEnabledForUserAsync(featureName, userEmail);
                    featureFlags.Add(new FeatureFlagResponse
                    {
                        FeatureName = featureName,
                        IsEnabled = isEnabled,
                        UserEmail = userEmail,
                        CheckedAt = DateTime.UtcNow
                    });
                }

                _logger.LogInformation("Checked {Count} feature flags for user email {Email}", featureFlags.Count, _taskContext.Email);

                return Ok(new ApiResponse<List<FeatureFlagResponse>>(
                    true,
                    StatusCodes.Status200OK,
                    "Feature flags checked successfully",
                    featureFlags));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking multiple feature flags for user email: {Email}", _taskContext.Email);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<object>(
                        false,
                        "An error occurred while checking feature flags",
                        data: null!,
                        StatusCodes.Status500InternalServerError,
                        new List<string> { ex.Message }));
            }
        }
    }

    /// <summary>
    /// Response model for feature flag status
    /// </summary>
    public class FeatureFlagResponse
    {
        /// <summary>
        /// Name of the feature flag
        /// </summary>
        public string FeatureName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the feature flag is enabled for the user
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// User email for whom the feature flag was checked
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// Description of what the feature flag controls
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Timestamp when the flag was checked
        /// </summary>
        public DateTime CheckedAt { get; set; }
    }


}
