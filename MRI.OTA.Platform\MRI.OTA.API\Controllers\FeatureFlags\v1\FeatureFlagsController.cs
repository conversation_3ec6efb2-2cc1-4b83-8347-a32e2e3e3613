using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.FeatureFlags;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.FeatureFlags.v1
{
    /// <summary>
    /// Controller to handle feature flags operations
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/feature-flags")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class FeatureFlagsController : ControllerBase
    {
        private readonly IFeatureFlagService _featureFlagService;
        private readonly ILogger<FeatureFlagsController> _logger;
        private readonly TaskContext _taskContext;

        /// <summary>
        /// Constructor for FeatureFlagsController
        /// </summary>
        /// <param name="featureFlagService">Feature flag service</param>
        /// <param name="logger">Logger service</param>
        /// <param name="taskContext">Task context containing user information</param>
        public FeatureFlagsController(
            IFeatureFlagService featureFlagService,
            ILogger<FeatureFlagsController> logger,
            TaskContext taskContext)
        {
            _featureFlagService = featureFlagService;
            _logger = logger;
            _taskContext = taskContext;
        }



        /// <summary>
        /// Get the status of all configured feature flags for the current user
        /// </summary>
        /// <returns>List of all feature flags and their statuses for the current user</returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Get all feature flags", Description = "Retrieves the current status of all configured feature flags for the authenticated user based on their email address")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<List<FeatureFlagResponseModel>>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ApiResponse<List<FeatureFlagResponseModel>>>> GetAllFeatureFlags()
        {
            try
            {
                _logger.LogInformation("Getting all feature flags status for user email: {Email}", _taskContext.Email);

                var userEmail = _taskContext.Email ?? string.Empty;
                var featureFlags = await _featureFlagService.GetAllFeatureFlagsForUserAsync(userEmail);

                _logger.LogInformation("Retrieved {Count} feature flags for user email {Email}", featureFlags.Count, _taskContext.Email);

                return Ok(new ApiResponse<List<FeatureFlagResponseModel>>(
                    true,
                    StatusCodes.Status200OK,
                    "Feature flags retrieved successfully",
                    featureFlags));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all feature flags for user email: {Email}", _taskContext.Email);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<object>(
                        false,
                        "An error occurred while retrieving feature flags",
                        data: null!,
                        StatusCodes.Status500InternalServerError,
                        new List<string> { ex.Message }));
            }
        }


    }
}
