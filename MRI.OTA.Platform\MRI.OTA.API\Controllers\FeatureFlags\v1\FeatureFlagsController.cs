using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.FeatureManagement;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.FeatureFlags.v1
{
    /// <summary>
    /// Controller to handle feature flags operations
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/feature-flags")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class FeatureFlagsController : ControllerBase
    {
        private readonly IFeatureManager _featureManager;
        private readonly ILogger<FeatureFlagsController> _logger;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructor for FeatureFlagsController
        /// </summary>
        /// <param name="featureManager">Feature manager service</param>
        /// <param name="logger">Logger service</param>
        /// <param name="configuration">Configuration service</param>
        public FeatureFlagsController(
            IFeatureManager featureManager,
            ILogger<FeatureFlagsController> logger,
            IConfiguration configuration)
        {
            _featureManager = featureManager;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Get the status of a specific feature flag
        /// </summary>
        /// <param name="featureName">Name of the feature flag</param>
        /// <returns>Feature flag status</returns>
        [HttpGet("{featureName}")]
        [SwaggerOperation(Summary = "Get feature flag status", Description = "Retrieves the current status of a specific feature flag")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<FeatureFlagResponse>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Feature flag not found")]
        public async Task<ActionResult<ApiResponse<FeatureFlagResponse>>> GetFeatureFlag(string featureName)
        {
            try
            {
                _logger.LogInformation("Getting feature flag status for: {FeatureName}", featureName);

                if (string.IsNullOrWhiteSpace(featureName))
                {
                    return BadRequest(new ApiResponse<object>(
                        false, 
                        "Feature name cannot be empty", 
                        data: null!, 
                        StatusCodes.Status400BadRequest, 
                        new List<string> { "Feature name is required" }));
                }

                var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                
                var response = new FeatureFlagResponse
                {
                    FeatureName = featureName,
                    IsEnabled = isEnabled,
                    CheckedAt = DateTime.UtcNow
                };

                _logger.LogInformation("Feature flag {FeatureName} is {Status}", featureName, isEnabled ? "enabled" : "disabled");

                return Ok(new ApiResponse<FeatureFlagResponse>(
                    true, 
                    MessagesConstants.SuccessMessage, 
                    response, 
                    StatusCodes.Status200OK));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feature flag status for: {FeatureName}", featureName);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ApiResponse<object>(
                        false, 
                        "An error occurred while retrieving the feature flag", 
                        data: null!, 
                        StatusCodes.Status500InternalServerError, 
                        new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Get the status of all configured feature flags
        /// </summary>
        /// <returns>List of all feature flags and their statuses</returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Get all feature flags", Description = "Retrieves the current status of all configured feature flags")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<List<FeatureFlagResponse>>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ApiResponse<List<FeatureFlagResponse>>>> GetAllFeatureFlags()
        {
            try
            {
                _logger.LogInformation("Getting all feature flags status");

                var featureFlags = new List<FeatureFlagResponse>();
                
                // Get all feature flags from configuration
                var featureManagementSection = _configuration.GetSection("FeatureManagement");
                var featureNames = featureManagementSection.GetChildren().Select(x => x.Key).ToList();

                foreach (var featureName in featureNames)
                {
                    var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                    featureFlags.Add(new FeatureFlagResponse
                    {
                        FeatureName = featureName,
                        IsEnabled = isEnabled,
                        CheckedAt = DateTime.UtcNow
                    });
                }

                _logger.LogInformation("Retrieved {Count} feature flags", featureFlags.Count);

                return Ok(new ApiResponse<List<FeatureFlagResponse>>(
                    true, 
                    MessagesConstants.SuccessMessage, 
                    featureFlags, 
                    StatusCodes.Status200OK));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all feature flags");
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ApiResponse<object>(
                        false, 
                        "An error occurred while retrieving feature flags", 
                        data: null!, 
                        StatusCodes.Status500InternalServerError, 
                        new List<string> { ex.Message }));
            }
        }

        /// <summary>
        /// Check multiple feature flags at once
        /// </summary>
        /// <param name="request">List of feature names to check</param>
        /// <returns>Status of requested feature flags</returns>
        [HttpPost("check")]
        [SwaggerOperation(Summary = "Check multiple feature flags", Description = "Checks the status of multiple feature flags in a single request")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ApiResponse<List<FeatureFlagResponse>>), description: "Success")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ApiResponse<List<FeatureFlagResponse>>>> CheckFeatureFlags([FromBody] FeatureFlagCheckRequest request)
        {
            try
            {
                _logger.LogInformation("Checking multiple feature flags: {FeatureNames}", string.Join(", ", request.FeatureNames));

                if (request?.FeatureNames == null || !request.FeatureNames.Any())
                {
                    return BadRequest(new ApiResponse<object>(
                        false, 
                        "Feature names list cannot be empty", 
                        data: null!, 
                        StatusCodes.Status400BadRequest, 
                        new List<string> { "At least one feature name is required" }));
                }

                var featureFlags = new List<FeatureFlagResponse>();

                foreach (var featureName in request.FeatureNames.Where(f => !string.IsNullOrWhiteSpace(f)))
                {
                    var isEnabled = await _featureManager.IsEnabledAsync(featureName);
                    featureFlags.Add(new FeatureFlagResponse
                    {
                        FeatureName = featureName,
                        IsEnabled = isEnabled,
                        CheckedAt = DateTime.UtcNow
                    });
                }

                _logger.LogInformation("Checked {Count} feature flags", featureFlags.Count);

                return Ok(new ApiResponse<List<FeatureFlagResponse>>(
                    true, 
                    MessagesConstants.SuccessMessage, 
                    featureFlags, 
                    StatusCodes.Status200OK));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking multiple feature flags");
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ApiResponse<object>(
                        false, 
                        "An error occurred while checking feature flags", 
                        data: null!, 
                        StatusCodes.Status500InternalServerError, 
                        new List<string> { ex.Message }));
            }
        }
    }

    /// <summary>
    /// Response model for feature flag status
    /// </summary>
    public class FeatureFlagResponse
    {
        /// <summary>
        /// Name of the feature flag
        /// </summary>
        public string FeatureName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the feature flag is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Timestamp when the flag was checked
        /// </summary>
        public DateTime CheckedAt { get; set; }
    }

    /// <summary>
    /// Request model for checking multiple feature flags
    /// </summary>
    public class FeatureFlagCheckRequest
    {
        /// <summary>
        /// List of feature flag names to check
        /// </summary>
        public List<string> FeatureNames { get; set; } = new List<string>();
    }
}
