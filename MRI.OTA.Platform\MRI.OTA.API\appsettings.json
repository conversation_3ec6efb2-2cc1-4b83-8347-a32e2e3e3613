{"ConnectionStrings": {"DefaultConnection": "#{ota-db-connection-string}"}, "ApplicationOption": {"BaseUrl": "#{ota-base-url}", "DefaultImageUrl": "#{ota-default-image-url}", "BatchSize": "#{ota-batch-size}"}, "Authentication": {"TenantId": "#{ota-tenant-id}", "TenantName": "#{ota-tenant-name}", "ClientId": "#{ota-client-id}", "ClientSecret": "#{ota-client-secret}", "B2CInstance": "#{ota-b2c-instance}", "Domain": "#{ota-b2c-domain}", "SignUpSignInPolicyId": "#{ota-signup-signin-policyid}", "Scopes": ["openid", "access_api"]}, "Cors": {"AllowedOrigins": "#{ota-api-allowed-origins}"}, "AzureStorage": {"ConnectionString": "#{ota-storage-connection-string}", "ContainerName": "#{ota-storage-container-name}"}, "ImageUploadSettings": {"AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"], "MaxFileSizeInBytes": 20}, "AzureCommunicationSettings": {"ConnectionString": "#{ota-azure-communication-connection-string}", "DefaultFromEmail": "#{ota-azure-communication-from-email}"}, "FeatureManagement": {"ConnectionString": "#{ota-feature-configuration-connection-string}"}, "RedisConfig": {"ConnectionString": "#{ota-redis-connection-string}", "DefaultExpirationMinutes": 35}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.ApplicationInsights"], "WriteTo": [{"Name": "ApplicationInsights", "Args": {"connectionString": "#{ota-app-insight-connection-string}", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithThreadName", "WithEventType"], "Properties": {"Application": "MRI OTA APIs"}}, "ApplicationInsights": {"ConnectionString": "#{ota-app-insight-connection-string}"}}