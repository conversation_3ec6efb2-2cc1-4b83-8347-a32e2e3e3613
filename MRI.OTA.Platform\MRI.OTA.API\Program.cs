using Asp.Versioning;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Data.SqlClient;
using Microsoft.FeatureManagement;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using MRI.OTA.API.ExceptionHandler;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Services;
using MRI.OTA.Application.Services.Integration;
using MRI.OTA.Common.Database;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Repository;
using MRI.OTA.Core.Repositories;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.DBCore.Repositories;
using MRI.OTA.Email;
using MRI.OTA.Infrastructure.Authentication;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Caching.Configuration;
using MRI.OTA.Infrastructure.Middlewares;
using MRI.OTA.Infrastructure.Middlewares.Authentication;
using MRI.OTA.Infrastructure.Telemetry;
using MRI.OTA.Integration.Configuration;
using Serilog;
using System.Data;
using System.IO.Compression;

var builder = WebApplication.CreateBuilder(args);
var configurationManager = builder.Configuration;
var tenantName = builder.Configuration["Authentication:TenantName"];
var tenantId = builder.Configuration["Authentication:TenantId"];
var clientId = builder.Configuration["Authentication:ClientId"];
var clientSecret = builder.Configuration["Authentication:ClientSecret"];
var policyId = builder.Configuration["Authentication:SignUpSignInPolicyId"];

///Add azure B2C Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.Authority = $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/v2.0/";
        options.Audience = clientId;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuers = new[]
            {
                $"https://{tenantName}.b2clogin.com/tfp/{tenantId}/{policyId}/v2.0/",
                $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/v2.0/",
                $"https://{tenantName}.b2clogin.com/{tenantId}/{policyId}/v2.0/",
                $"https://{tenantName}.b2clogin.com/{tenantId}/v2.0/",
                $"https://login.microsoftonline.com/{tenantId}/v2.0",
                $"https://sts.windows.net/{tenantId}/"
            },
            ValidateAudience = true,
            ValidAudience = clientId,
            ValidateLifetime = true
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("ApiKeyPolicy", policy => policy.Requirements.Add(new ApiKeyRequirement()));
});

// Add services to the container.
IntegrationModule.ConfigureIntegration(builder.Services, builder.Configuration);

// Configure Redis Cache
builder.Services.ConfigureRedisCache(builder.Configuration);
builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

// Configure FluentValidation
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddFluentValidationClientsideAdapters();
builder.Services.AddValidatorsFromAssemblies(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddControllers();

// Add HttpClient
builder.Services.AddHttpClient();

// Exception Handlers
builder.Services.AddScoped<TaskContext>();
builder.Services.AddExceptionHandler<BadRequestExceptionHandler>();
builder.Services.AddExceptionHandler<NotFoundExceptionHandler>();
builder.Services.AddExceptionHandler<ConnectionTimeoutExceptionHandler>();
builder.Services.AddExceptionHandler<ConflictExceptionHandler>();
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddScoped<IAuthorizationHandler, ApiKeyAuthorizationHandler>();
builder.Services.AddProblemDetails();


// Add API Versioning
builder.Services.AddApiVersioning(options =>
{
    options.ReportApiVersions = true;
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.DefaultApiVersion = new ApiVersion(1, 0);
}).AddApiExplorer(options =>
{
    options.GroupNameFormat = "'v'VVV";
    options.SubstituteApiVersionInUrl = true;
});

builder.Services.AddEndpointsApiExplorer();

builder.Services.AddApplicationInsightsTelemetry();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<ITelemetryInitializer, CustomTelemetryInitializer>();

// Add Feature Management
builder.Services.AddFeatureManagement();
builder.Host.UseSerilog((context, services, loggerConfiguration) =>
{
    loggerConfiguration
        .ReadFrom.Configuration(context.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .Enrich.WithThreadId()
        .Enrich.WithEnvironmentName()
        .WriteTo.ApplicationInsights(
            services.GetRequiredService<TelemetryConfiguration>(),
            TelemetryConverter.Traces);
});

// Configure the database connection
builder.Services.AddSingleton<IDbConnection>((sp) =>
{
    var sqlConnection = new SqlConnection(configurationManager.GetConnectionString("DefaultConnection"));
    sqlConnection.StatisticsEnabled = true;
    return sqlConnection;
});

// Add SQL connection configuration
builder.Services.Configure<SqlConnectionConfiguration>(options =>
{
    options.MaxPoolSize = 100; // Adjust based on your needs
    options.MinPoolSize = 10;
    options.ConnectionTimeout = 30; // seconds
    options.CommandTimeout = 30;    // seconds
    options.EnableRetryLogic = true;
    options.MaxRetryAttempts = 3;
    options.RetryInterval = TimeSpan.FromSeconds(1);
});

var connectionString = configurationManager.GetConnectionString("DefaultConnection");
// Register the connection factory
builder.Services.AddSingleton<IDbConnectionFactory>(provider =>
    new SqlConnectionFactory(connectionString));

// Register the Dapper wrapper
builder.Services.AddSingleton<IDapperWrapper, DapperWrapper>();

// Register your repository
builder.Services.AddScoped<IDataSourceRepository, DataSourceRepository>();

// Register health check services
builder.Services.AddHealthChecks();
// Register application services
builder.Services.AddSingleton<IJwtTokenValidator, JwtTokenValidator>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IPropertyRepository, PropertyRepository>();
builder.Services.AddScoped<IPropertyService, PropertyService>();
builder.Services.AddScoped<IMasterService, MasterService>();
builder.Services.AddScoped<IMasterRepository, MasterRepository>();
builder.Services.AddScoped<IDataSourceRepository, DataSourceRepository>();
builder.Services.AddScoped<IDataSourceService, DataSourceService>();
builder.Services.AddScoped<IEventConsumerService, EventConsumerService>();
builder.Services.AddScoped<IEventConsumerRepository, EventConsumerRepository>();
builder.Services.AddScoped<IImageStorageService, ImageStorageService>();
builder.Services.AddScoped<IImageRepository, ImageRepository>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IInvitationService, InvitationService>();
builder.Services.AddScoped<IInvitationRepository, InvitationRepository>();
builder.Services.AddScoped<IEmailServiceFactory, EmailServiceFactory>();
builder.Services.AddScoped<IBatchOperationLogger, BatchOperationLogger>();
builder.Services.AddScoped<IPropertTreeService, PropertTreeService>();
builder.Services.AddScoped<IAzureB2CTokenService, AzureB2CTokenService>();
builder.Services.AddScoped<IAPITrackingService, APITrackingService>();
builder.Services.AddScoped<IAPITrackingRepository, APITrackingRepository>();
builder.Services.AddScoped<IIntegrationRepository, IntegrationRepository>();
builder.Services.AddScoped<INotificationRepository, NotificationRepository>();
builder.Services.AddScoped<IFeatureFlagService, FeatureFlagService>();

builder.Services.AddEndpointsApiExplorer();

// Register Swagger for API documentation
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "OTA API - V1", Version = "v1.0" });

    // Add OAuth2 configuration
    c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.OAuth2,
        Flows = new OpenApiOAuthFlows
        {
            AuthorizationCode = new OpenApiOAuthFlow
            {
                AuthorizationUrl = new Uri($"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/oauth2/v2.0/authorize"),
                TokenUrl = new Uri($"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/oauth2/v2.0/token"),
                Scopes = configurationManager.GetSection("Authentication:Scopes").Get<string[]>().ToDictionary(item => $"https://{tenantName}.onmicrosoft.com/{clientId}/{item}")

            }
        }
    });

    // Add global security requirement
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "oauth2" }
            },
            new List<string>()
        }
    });
});

var app = builder.Build();
app.UseExceptionHandler();

app.UseRouting();

builder.Services.AddCors();

app.UseCors(builder =>
{
    var allowedOrigins = configurationManager.GetValue<string>("Cors:AllowedOrigins");
    if (!string.IsNullOrEmpty(allowedOrigins))
    {
        builder.WithOrigins(allowedOrigins.Split(",").Select(x => x.Trim()).ToArray())
            .AllowAnyHeader()
            .WithMethods("GET", "PUT", "POST", "DELETE", "OPTIONS")
            .SetPreflightMaxAge(TimeSpan.FromSeconds(3600));
    }
    else
    {
        builder.AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod();
    }
});

app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "OTA API V1");

    c.OAuthClientId(clientId);
    c.OAuthUsePkce();
    c.OAuthScopeSeparator(" ");
});

// Map the health check endpoint
app.MapHealthChecks("/healthz").AllowAnonymous();

app.UseHttpsRedirection();

app.UseMiddleware<SecurityHeadersMiddleware>();

app.UseAuthentication();
app.UseMiddleware<AuthenticationMiddleware>();

app.UseAuthorization();

app.UseMiddleware<TaskContextMiddleware>();

app.MapControllers();

string launchProfile = Environment.GetEnvironmentVariable("MRI_LAUNCH_PROFILE") ?? "Unknown";
Console.WriteLine("Warning: Using launch profile " + launchProfile);
Console.WriteLine("Warning: Using environment " + app.Environment.EnvironmentName);
app.Run();

