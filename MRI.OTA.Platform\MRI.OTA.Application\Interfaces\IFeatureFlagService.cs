using MRI.OTA.Application.Models.FeatureFlags;

namespace MRI.OTA.Application.Interfaces
{
    /// <summary>
    /// Interface for feature flag service operations
    /// </summary>
    public interface IFeatureFlagService
    {
        /// <summary>
        /// Get all configured feature flags from Azure App Configuration for a specific user
        /// </summary>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>List of feature flag response models</returns>
        Task<List<FeatureFlagResponseModel>> GetAllFeatureFlagsForUserAsync(string userIdentifier);
    }
}
