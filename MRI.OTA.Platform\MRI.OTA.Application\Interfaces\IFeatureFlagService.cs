using MRI.OTA.Application.Models.FeatureFlags;

namespace MRI.OTA.Application.Interfaces
{
    /// <summary>
    /// Interface for feature flag service operations
    /// </summary>
    public interface IFeatureFlagService
    {
        /// <summary>
        /// Check if a feature is enabled
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>True if feature is enabled, false otherwise</returns>
        Task<bool> IsFeatureEnabledAsync(string featureName);

        /// <summary>
        /// Check if a feature is enabled for a specific user
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>True if feature is enabled for the user, false otherwise</returns>
        Task<bool> IsFeatureEnabledForUserAsync(string featureName, string userIdentifier);

        /// <summary>
        /// Get all configured feature flags with their current status
        /// </summary>
        /// <returns>List of feature flag information</returns>
        Task<List<FeatureFlagInfo>> GetAllFeatureFlagsAsync();

        /// <summary>
        /// Get all configured feature flags with their current status for a specific user
        /// </summary>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>List of feature flag response models</returns>
        Task<List<FeatureFlagResponseModel>> GetAllFeatureFlagsForUserAsync(string userIdentifier);

        /// <summary>
        /// Get feature flag information by name
        /// </summary>
        /// <param name="featureName">Name of the feature</param>
        /// <returns>Feature flag information</returns>
        Task<FeatureFlagInfo?> GetFeatureFlagAsync(string featureName);

        /// <summary>
        /// Check multiple feature flags at once
        /// </summary>
        /// <param name="featureNames">List of feature names to check</param>
        /// <returns>Dictionary with feature names and their status</returns>
        Task<Dictionary<string, bool>> CheckMultipleFeaturesAsync(IEnumerable<string> featureNames);

        /// <summary>
        /// Check multiple feature flags for a specific user
        /// </summary>
        /// <param name="featureNames">List of feature names to check</param>
        /// <param name="userIdentifier">User identifier (email or user ID)</param>
        /// <returns>Dictionary with feature names and their status for the user</returns>
        Task<Dictionary<string, bool>> CheckMultipleFeaturesForUserAsync(IEnumerable<string> featureNames, string userIdentifier);
    }
}
